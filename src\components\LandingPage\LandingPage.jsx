import {
  Box,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Button,
  Chip,
  <PERSON>ing,
  Badge,
  IconButton,
  Divider,
  Grid,
  Container
} from '@mui/material';
import {
  FavoriteOutlined,
  ShareOutlined,
  ShoppingCartOutlined,
  LocalShippingOutlined,
  SecurityOutlined,
  VisibilityOutlined,
  BuildOutlined
} from '@mui/icons-material';
import './LandingPage.css';

// Aftermarket products data
const aftermarketProducts = [
  {
    id: 1,
    brand: 'PERFORMANCE PLUS',
    title: 'High-Performance Cold Air Intake System',
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=250&fit=crop',
    rating: 4.7,
    reviews: 1234,
    price: 299.99,
    originalPrice: 399.99,
    features: ['+15 HP', 'Easy Install', 'Lifetime Warranty'],
    shipping: 'Free shipping • Arrives in 2-3 days',
    badge: 'PERFORMANCE',
    badgeColor: 'error',
    category: 'Engine Performance'
  },
  {
    id: 2,
    brand: 'TUNER TECH',
    title: 'ECU Performance Chip Tuning Module',
    image: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=400&h=250&fit=crop',
    rating: 4.8,
    reviews: 892,
    price: 199.99,
    originalPrice: null,
    features: ['+20% Power', 'Plug & Play', 'Reversible'],
    shipping: 'Free shipping • Same day processing',
    badge: 'BEST SELLER',
    badgeColor: 'warning',
    category: 'Engine Tuning'
  },
  {
    id: 3,
    brand: 'EXHAUST PRO',
    title: 'Cat-Back Exhaust System - Stainless Steel',
    image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=250&fit=crop',
    rating: 4.6,
    reviews: 567,
    price: 449.99,
    originalPrice: 599.99,
    features: ['304 Stainless', 'Deep Sound', 'Bolt-On'],
    shipping: 'Free shipping • Professional installation available',
    badge: 'PREMIUM',
    badgeColor: 'success',
    category: 'Exhaust Systems'
  },
  {
    id: 4,
    brand: 'SUSPENSION WORKS',
    title: 'Adjustable Coilover Suspension Kit',
    image: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=400&h=250&fit=crop',
    rating: 4.9,
    reviews: 445,
    price: 899.99,
    originalPrice: 1199.99,
    features: ['32-Way Adjust', 'Track Ready', 'Lifetime Support'],
    shipping: 'Free shipping • Professional installation recommended',
    badge: 'TRACK PROVEN',
    badgeColor: 'error',
    category: 'Suspension'
  },
  {
    id: 5,
    brand: 'BRAKE MASTER',
    title: 'Big Brake Kit - 6-Piston Calipers',
    image: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=250&fit=crop',
    rating: 4.8,
    reviews: 321,
    price: 1299.99,
    originalPrice: 1599.99,
    features: ['6-Piston', 'Slotted Rotors', 'Track Pads'],
    shipping: 'Free shipping • Professional installation required',
    badge: 'RACE SPEC',
    badgeColor: 'error',
    category: 'Braking Systems'
  },
  {
    id: 6,
    brand: 'AERO DYNAMICS',
    title: 'Carbon Fiber Front Splitter Kit',
    image: 'https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?w=400&h=250&fit=crop',
    rating: 4.5,
    reviews: 234,
    price: 599.99,
    originalPrice: null,
    features: ['Real Carbon', 'Lightweight', 'Track Tested'],
    shipping: 'Free shipping • Custom fitment available',
    badge: 'CARBON FIBER',
    badgeColor: 'success',
    category: 'Aerodynamics'
  }
];

const LandingPage = ({ onViewProduct }) => {
  return (
    <Container maxWidth="lg" sx={{ py: 4, overflowX: 'hidden' }}>
      {/* Page Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold', mb: 2 }}>
          Aftermarket Performance Parts
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Upgrade your vehicle with premium aftermarket components
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {aftermarketProducts.map((product) => (
          <Grid item xs={12} sm={6} md={4} key={product.id}>
            <Card
              className="product-card"
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                transition: 'transform 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 24px rgba(0,0,0,0.15)'
                }
              }}
            >
              {/* Badge */}
              <Badge
                badgeContent={product.badge}
                color={product.badgeColor}
                sx={{
                  position: 'absolute',
                  top: 16,
                  left: 16,
                  zIndex: 1,
                  '& .MuiBadge-badge': {
                    fontSize: '0.75rem',
                    fontWeight: 'bold'
                  }
                }}
              />

              {/* Action Buttons */}
              <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1 }}>
                <IconButton
                  size="small"
                  sx={{ bgcolor: 'white', mb: 1, '&:hover': { bgcolor: 'primary.main', color: 'white' } }}
                  onClick={() => onViewProduct(product)}
                >
                  <VisibilityOutlined />
                </IconButton>
                <IconButton size="small" sx={{ bgcolor: 'white', mb: 1, '&:hover': { color: 'red' } }}>
                  <FavoriteOutlined />
                </IconButton>
                <IconButton size="small" sx={{ bgcolor: 'white', display: 'block' }}>
                  <ShareOutlined />
                </IconButton>
              </Box>

              {/* Product Image */}
              <CardMedia
                component="img"
                height="250"
                image={product.image}
                alt={product.title}
                sx={{ objectFit: 'cover' }}
              />

              <CardContent sx={{ flexGrow: 1, p: 2 }}>
                {/* Brand & Category */}
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="caption" color="primary" sx={{ fontWeight: 'bold' }}>
                    {product.brand}
                  </Typography>
                  <Chip
                    icon={<BuildOutlined />}
                    label={product.category}
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                </Box>

                {/* Product Title */}
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    fontWeight: 'bold',
                    mb: 1,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}
                >
                  {product.title}
                </Typography>

                {/* Rating & Reviews */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Rating value={product.rating} precision={0.1} size="small" readOnly />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                    {product.rating} ({product.reviews.toLocaleString()} reviews)
                  </Typography>
                </Box>

                {/* Features */}
                <Box sx={{ mb: 2 }}>
                  {product.features.map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      size="small"
                      sx={{ mr: 0.5, mb: 0.5 }}
                    />
                  ))}
                </Box>

                {/* Price */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h5" component="span" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                    ${product.price}
                  </Typography>
                  {product.originalPrice && (
                    <Typography
                      variant="body2"
                      component="span"
                      sx={{
                        textDecoration: 'line-through',
                        color: 'text.secondary',
                        ml: 1
                      }}
                    >
                      ${product.originalPrice}
                    </Typography>
                  )}
                </Box>

                {/* Shipping Info */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <LocalShippingOutlined sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                  <Typography variant="caption" color="success.main">
                    {product.shipping}
                  </Typography>
                </Box>

                {/* Security Badge */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <SecurityOutlined sx={{ fontSize: 16, color: 'primary.main', mr: 0.5 }} />
                  <Typography variant="caption" color="text.secondary">
                    Secure payment • Warranty included
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* Action Buttons */}
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    startIcon={<ShoppingCartOutlined />}
                    fullWidth
                    sx={{
                      bgcolor: 'primary.main',
                      '&:hover': { bgcolor: 'primary.dark' }
                    }}
                  >
                    Add to Cart
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<VisibilityOutlined />}
                    sx={{ minWidth: 'auto', px: 2 }}
                    onClick={() => onViewProduct(product)}
                  >
                    View
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
};

export default LandingPage;