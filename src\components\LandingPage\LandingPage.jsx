import React from 'react';
import {
  Box,
  Paper,
  AppBar,
  Toolbar,
  Typography
} from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

const theme = createTheme({
  palette: {
    primary: {
      main: '#a2a5a8',
    },
    secondary: {
      main: '#2ECCCB',
    },
    background: {
      default: '#ECF3F8',
      paper: '#FFFFFF',
    },
  },
});

const LandingPage = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          width: '100vw',
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          bgcolor: 'background.default',
          margin: 0,
          padding: 0,
          boxSizing: 'border-box',
        }}
      >
        {/* Top Navigation Bar */}
        <AppBar
          position="static"
          sx={{
            height: 60,
            minHeight: 60,
            flexShrink: 0,
            width: '100%',
          }}
        >
          <Toolbar sx={{ minHeight: '60px !important', height: 60, width: '100%' }}>
            <Typography variant="h6" component="div">

            </Typography>
          </Toolbar>
        </AppBar>

        {/* Main Content Area */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            gap: 0.25,
            p: 0.25,
            minHeight: 0,
            overflow: 'hidden',
            width: '100%',
            boxSizing: 'border-box',
          }}
        >
          {/* Left Sidebar */}
          <Paper
            elevation={1}
            sx={{
              width: 200,
              flexShrink: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '2px solid #DCE6F0',
              boxSizing: 'border-box',
            }}
          >
            <Typography></Typography>
          </Paper>

          {/* Center Content */}
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              gap: 0.25,
              minHeight: 0,
              minWidth: 0,
              overflow: 'hidden',
            }}
          >
            {/* Top Section */}
            <Box
              sx={{
                display: 'flex',
                gap: 0.25,
                flex: 3,
                minHeight: 0,
                overflow: 'hidden',
              }}
            >
              {/* Hero Section */}
              <Paper
                elevation={1}
                sx={{
                  flex: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '2px solid #DCE6F0',
                  minWidth: 0,
                  overflow: 'hidden',
                }}
              >
                <Typography></Typography>
              </Paper>

              {/* Right Grid */}
              <Box
                sx={{
                  flex: 1,
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gridTemplateRows: '1fr 1fr 1fr',
                  gap: 0.25,
                  minWidth: 0,
                  overflow: 'hidden',
                }}
              >
                {[1, 2, 3, 4, 5, 6].map((item) => (
                  <Paper
                    key={item}
                    elevation={1}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: '2px solid #DCE6F0',
                      minWidth: 0,
                      minHeight: 0,
                      overflow: 'hidden',
                    }}
                  >
                    <Typography></Typography>
                  </Paper>
                ))}
              </Box>
            </Box>

            {/* Bottom Section */}
            <Box
              sx={{
                display: 'flex',
                gap: 0.25,
                flex: 2,
                minHeight: 0,
                overflow: 'hidden',
              }}
            >
              {/* Left Column */}
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 0.25,
                  minWidth: 0,
                  overflow: 'hidden',
                }}
              >
                {[1, 2, 3].map((item) => (
                  <Paper
                    key={item}
                    elevation={1}
                    sx={{
                      flex: 1,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: '2px solid #DCE6F0',
                      minHeight: 0,
                      overflow: 'hidden',
                    }}
                  >
                    <Typography></Typography>
                  </Paper>
                ))}
              </Box>

              {/* Right Column */}
              <Box
                sx={{
                  flex: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 0.25,
                  minWidth: 0,
                  overflow: 'hidden',
                }}
              >
                {[1, 2].map((item) => (
                  <Paper
                    key={item}
                    elevation={1}
                    sx={{
                      flex: 1,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: '2px solid #DCE6F0',
                      minHeight: 0,
                      overflow: 'hidden',
                    }}
                  >
                    <Typography></Typography>
                  </Paper>
                ))}
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default LandingPage;
