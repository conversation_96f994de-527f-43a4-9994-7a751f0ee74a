import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Button,
  Chip,
  Rating,
  Badge,
  IconButton,
  Divider,
  Grid,
  Container,
  Stack,
  Tooltip,
  LinearProgress
} from '@mui/material';
// Icons will be added after installing @mui/icons-material
import './LandingPage.css';

// Import JSON data
import productsData from '../../data/aftermarketProducts.json';

// Aftermarket products data

const LandingPage = ({ onViewProduct }) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load products from JSON data
    setProducts(productsData.products);
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <Typography variant="h6">Loading products...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4, overflowX: 'hidden' }}>
      {/* Page Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold', mb: 2 }}>
          Aftermarket Performance Parts
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Upgrade your vehicle with premium aftermarket components
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {products.map((product) => (
          <Grid item xs={12} sm={6} md={4} key={product.id}>
            <Card
              className="product-card"
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                transition: 'transform 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 24px rgba(0,0,0,0.15)'
                }
              }}
            >
              {/* Badge */}
              <Badge
                badgeContent={product.badge}
                color={product.badgeColor}
                sx={{
                  position: 'absolute',
                  top: 16,
                  left: 16,
                  zIndex: 1,
                  '& .MuiBadge-badge': {
                    fontSize: '0.75rem',
                    fontWeight: 'bold'
                  }
                }}
              />

              {/* Action Buttons */}
              <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1 }}>
                <Button
                  size="small"
                  variant="contained"
                  sx={{ bgcolor: 'white', color: 'primary.main', mb: 1, '&:hover': { bgcolor: 'primary.main', color: 'white' } }}
                  onClick={() => onViewProduct(product)}
                >
                  👁️
                </Button>
              </Box>

              {/* Product Image */}
              <CardMedia
                component="img"
                height="250"
                image={product.images[0]}
                alt={product.productName}
                sx={{ objectFit: 'cover' }}
              />

              <CardContent sx={{ flexGrow: 1, p: 2 }}>
                {/* Brand & Model Number */}
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="caption" color="primary" sx={{ fontWeight: 'bold' }}>
                    {product.brand}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    SKU: {product.modelNumber}
                  </Typography>
                </Box>

                {/* Product Title & Short Description */}
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    fontWeight: 'bold',
                    mb: 1,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}
                >
                  {product.productName}
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {product.shortDescription}
                </Typography>

                {/* Rating & Reviews */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Rating value={product.ratings.average} precision={0.1} size="small" readOnly />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                    {product.ratings.average} ({product.ratings.totalReviews.toLocaleString()} reviews)
                  </Typography>
                  <Chip label={`${product.ratings.verifiedBuyers} verified`} size="small" sx={{ ml: 1 }} />
                </Box>

                {/* Vehicle Compatibility */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                    🚗 Fits: {product.fitment.vehicleCompatibility}
                  </Typography>
                  {product.fitment.fitmentGuarantee && (
                    <Chip label="Fitment Guarantee" size="small" color="success" sx={{ mt: 0.5 }} />
                  )}
                </Box>

                {/* Technical Features */}
                <Box sx={{ mb: 2 }}>
                  {product.technicalDetails.specialFeatures.map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      size="small"
                      sx={{ mr: 0.5, mb: 0.5 }}
                    />
                  ))}
                </Box>

                {/* Pricing & Offers */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h5" component="span" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                    ${product.pricing.offerPrice}
                  </Typography>
                  {product.pricing.retailPrice !== product.pricing.offerPrice && (
                    <>
                      <Typography
                        variant="body2"
                        component="span"
                        sx={{
                          textDecoration: 'line-through',
                          color: 'text.secondary',
                          ml: 1
                        }}
                      >
                        ${product.pricing.retailPrice}
                      </Typography>
                      <Chip
                        label={`Save ${product.pricing.savings.percentage}%`}
                        size="small"
                        color="error"
                        sx={{ ml: 1 }}
                      />
                    </>
                  )}
                  {product.pricing.timeBasedOffer && (
                    <Typography variant="caption" color="error.main" sx={{ display: 'block', mt: 0.5 }}>
                      ⏰ {product.pricing.timeBasedOffer}
                    </Typography>
                  )}
                  {product.pricing.emiOptions && (
                    <Typography variant="caption" color="primary.main" sx={{ display: 'block' }}>
                      💳 EMI: {product.pricing.emiOptions}
                    </Typography>
                  )}
                </Box>

                {/* Stock & Availability */}
                <Box sx={{ mb: 2 }}>
                  <Chip
                    label={product.availability.stockStatus}
                    size="small"
                    color={product.availability.stockStatus === 'In Stock' ? 'success' : 'warning'}
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    📦 {product.availability.estimatedDelivery}
                  </Typography>
                </Box>

                {/* Shipping & Delivery */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="success.main" sx={{ display: 'block' }}>
                    🚚 {product.availability.freeShipping ? 'Free Shipping' : `Shipping: $${product.availability.shippingCharges}`}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {product.availability.codAvailable ? '💰 COD Available' : ''} • {product.availability.returnPolicy}
                  </Typography>
                </Box>

                {/* Technical Details */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                    📏 {product.technicalDetails.dimensions} • {product.technicalDetails.weight}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    � {product.technicalDetails.installationType} • {product.warranty}
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* Action Buttons */}
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    fullWidth
                    sx={{
                      bgcolor: 'primary.main',
                      '&:hover': { bgcolor: 'primary.dark' }
                    }}
                  >
                    🛒 Add to Cart
                  </Button>
                  <Button
                    variant="outlined"
                    sx={{ minWidth: 'auto', px: 2 }}
                    onClick={() => onViewProduct(product)}
                  >
                    👁️ View
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
};

export default LandingPage;