import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Card,
  CardMedia,
  Typography,
  Rating,
  Chip,
  Button,
  Box,
  Divider,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  Stack,
  Avatar,
  LinearProgress
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Visibility,
  ShoppingCart,
  LocalShipping,
  Security,
  Verified,
  ThreeSixty,
  ZoomIn,
  CameraAlt
} from '@mui/icons-material';
import './LandingPage.css';

const LandingPage = ({ onViewProduct }) => {
  const [products, setProducts] = useState([]);
  const [selectedMake, setSelectedMake] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  const [selectedYear, setSelectedYear] = useState('');
  const [pincode, setPincode] = useState('');

  useEffect(() => {
    // Load products from JSON
    fetch('/src/data/aftermarketProducts.json')
      .then(response => response.json())
      .then(data => setProducts(data.products))
      .catch(error => console.error('Error loading products:', error));
  }, []);

  const ProductCard = ({ product }) => {
    const [selectedImage, setSelectedImage] = useState(0);
    const [selectedColor, setSelectedColor] = useState(product.variants?.colors?.[0] || '');
    const [selectedSize, setSelectedSize] = useState(product.variants?.sizes?.[0] || '');
    const [quantity, setQuantity] = useState(1);
    const [isFavorite, setIsFavorite] = useState(false);

    return (
      <Card className="product-card" sx={{ maxWidth: 1200, mx: 'auto', mb: 4 }}>
        <Grid container>
          {/* Left Vertical Strip - Product Image Thumbnails & Variant Swatches */}
          <Grid item xs={12} md={2}>
            <Box sx={{ p: 2, borderRight: '1px solid #e0e0e0', height: '100%' }}>
              {/* Product Image Thumbnails */}
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                Product Views
              </Typography>
              <Stack spacing={1} sx={{ mb: 2 }}>
                {product.images?.map((image, index) => (
                  <Card
                    key={index}
                    variant="thumbnail"
                    onClick={() => setSelectedImage(index)}
                    sx={{
                      cursor: 'pointer',
                      border: selectedImage === index ? '2px solid primary.main' : '1px solid #e0e0e0'
                    }}
                  >
                    <CardMedia
                      component="img"
                      height="60"
                      image={image}
                      alt={`View ${index + 1}`}
                      sx={{ objectFit: 'cover' }}
                    />
                  </Card>
                ))}
              </Stack>

              {/* Variant Swatches - Colors */}
              {product.variants?.colors && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 1 }}>
                    Colors
                  </Typography>
                  <Stack direction="row" spacing={0.5} flexWrap="wrap">
                    {product.variants.colors.map((color) => (
                      <Box
                        key={color}
                        onClick={() => setSelectedColor(color)}
                        sx={{
                          width: 24,
                          height: 24,
                          borderRadius: '50%',
                          backgroundColor: color.toLowerCase(),
                          border: selectedColor === color ? '2px solid primary.main' : '1px solid #ccc',
                          cursor: 'pointer',
                          mb: 0.5
                        }}
                        title={color}
                      />
                    ))}
                  </Stack>
                </Box>
              )}

              {/* Variant Swatches - Sizes */}
              {product.variants?.sizes && (
                <Box>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 1 }}>
                    Sizes
                  </Typography>
                  <Stack direction="row" spacing={0.5} flexWrap="wrap">
                    {product.variants.sizes.map((size) => (
                      <Chip
                        key={size}
                        label={size}
                        size="small"
                        variant={selectedSize === size ? "filled" : "outlined"}
                        onClick={() => setSelectedSize(size)}
                        sx={{ cursor: 'pointer', mb: 0.5 }}
                      />
                    ))}
                  </Stack>
                </Box>
              )}
            </Box>
          </Grid>

          {/* Center Top (Large Block) - Main Product Image */}
          <Grid item xs={12} md={5}>
            <Box sx={{ p: 2, position: 'relative' }}>
              <Card sx={{ position: 'relative', overflow: 'hidden' }}>
                <CardMedia
                  component="img"
                  height="400"
                  image={product.images?.[selectedImage] || product.images?.[0]}
                  alt={product.productName}
                  sx={{ objectFit: 'cover' }}
                />

                {/* Image Controls Overlay */}
                <Box sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  display: 'flex',
                  gap: 1
                }}>
                  <IconButton size="small" sx={{ bgcolor: 'rgba(255,255,255,0.8)' }}>
                    <ZoomIn />
                  </IconButton>
                  <IconButton size="small" sx={{ bgcolor: 'rgba(255,255,255,0.8)' }}>
                    <ThreeSixty />
                  </IconButton>
                  <IconButton size="small" sx={{ bgcolor: 'rgba(255,255,255,0.8)' }}>
                    <CameraAlt />
                  </IconButton>
                </Box>

                {/* Badge Overlay */}
                {product.badge && (
                  <Chip
                    label={product.badge}
                    color={product.badgeColor || 'primary'}
                    sx={{
                      position: 'absolute',
                      top: 8,
                      left: 8,
                      fontWeight: 'bold'
                    }}
                  />
                )}
              </Card>

              {/* Short Description & Key Features */}
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                  {product.shortDescription}
                </Typography>

                {/* Key Features */}
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Key Features:
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mb: 1 }}>
                  {product.features?.map((feature, index) => (
                    <Chip key={index} label={feature} size="small" variant="outlined" />
                  ))}
                </Stack>

                {/* Use Cases & Technical Highlights */}
                <Typography variant="caption" sx={{ display: 'block', color: 'text.secondary' }}>
                  Category: {product.category} | Usage: {product.technicalDetails?.usage}
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Right Section - Product Details Grid */}
          <Grid item xs={12} md={5}>
            <Box sx={{ p: 2 }}>
              <Grid container spacing={2}>
                {/* Row 1 Left - Product Title, Brand, Part Number */}
                <Grid item xs={6}>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                      {product.productName}
                    </Typography>
                    <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 'bold' }}>
                      {product.brand}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      SKU: {product.modelNumber}
                    </Typography>
                  </Box>
                </Grid>

                {/* Row 1 Right - Star Rating & Reviews */}
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'right' }}>
                    <Rating
                      value={product.ratings?.average || 0}
                      precision={0.1}
                      readOnly
                      size="small"
                    />
                    <Typography variant="body2" sx={{ mt: 0.5 }}>
                      {product.ratings?.average} ({product.ratings?.totalReviews?.toLocaleString()} reviews)
                    </Typography>
                    <Chip
                      label={`${product.ratings?.verifiedBuyers} verified`}
                      size="small"
                      color="success"
                      icon={<Verified />}
                    />
                  </Box>
                </Grid>

                {/* Row 2 Left - Pricing */}
                <Grid item xs={6}>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                      ${product.pricing?.offerPrice}
                    </Typography>
                    {product.pricing?.retailPrice !== product.pricing?.offerPrice && (
                      <>
                        <Typography
                          variant="body2"
                          sx={{ textDecoration: 'line-through', color: 'text.secondary' }}
                        >
                          ${product.pricing?.retailPrice}
                        </Typography>
                        <Chip
                          label={`You Save ${product.pricing?.savings?.percentage}%`}
                          size="small"
                          color="error"
                        />
                      </>
                    )}
                    {product.pricing?.timeBasedOffer && (
                      <Typography variant="caption" color="error.main" sx={{ display: 'block' }}>
                        ⏰ {product.pricing.timeBasedOffer}
                      </Typography>
                    )}
                    {product.pricing?.emiOptions && (
                      <Typography variant="caption" color="primary.main">
                        💳 {product.pricing.emiOptions}
                      </Typography>
                    )}
                  </Box>
                </Grid>

                {/* Row 2 Right - Stock Status & Delivery */}
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'right' }}>
                    <Chip
                      label={product.availability?.stockStatus || 'In Stock'}
                      color={product.availability?.stockStatus === 'In Stock' ? 'success' : 'warning'}
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                      📦 {product.availability?.estimatedDelivery}
                    </Typography>
                    {product.availability?.freeShipping && (
                      <Chip
                        label="Free Shipping"
                        size="small"
                        color="success"
                        icon={<LocalShipping />}
                      />
                    )}

                    {/* Pincode Delivery Checker */}
                    <TextField
                      size="small"
                      placeholder="Enter pincode"
                      value={pincode}
                      onChange={(e) => setPincode(e.target.value)}
                      sx={{ mt: 1, width: '120px' }}
                    />
                  </Box>
                </Grid>

                {/* Row 3 Left - Fitment Information */}
                <Grid item xs={6}>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                      🚗 Vehicle Fitment
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      {product.fitment?.vehicleCompatibility}
                    </Typography>

                    {/* Vehicle Selector */}
                    <Stack spacing={1}>
                      <FormControl size="small" fullWidth>
                        <InputLabel>Make</InputLabel>
                        <Select
                          value={selectedMake}
                          onChange={(e) => setSelectedMake(e.target.value)}
                        >
                          {product.fitment?.makeModelYear?.makes?.map((make) => (
                            <MenuItem key={make} value={make}>{make}</MenuItem>
                          ))}
                        </Select>
                      </FormControl>

                      <FormControl size="small" fullWidth>
                        <InputLabel>Model</InputLabel>
                        <Select
                          value={selectedModel}
                          onChange={(e) => setSelectedModel(e.target.value)}
                        >
                          {product.fitment?.makeModelYear?.models?.map((model) => (
                            <MenuItem key={model} value={model}>{model}</MenuItem>
                          ))}
                        </Select>
                      </FormControl>

                      <FormControl size="small" fullWidth>
                        <InputLabel>Year</InputLabel>
                        <Select
                          value={selectedYear}
                          onChange={(e) => setSelectedYear(e.target.value)}
                        >
                          {product.fitment?.makeModelYear?.years?.map((year) => (
                            <MenuItem key={year} value={year}>{year}</MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Stack>

                    {product.fitment?.fitmentGuarantee && (
                      <Chip
                        label="Fitment Guarantee"
                        size="small"
                        color="success"
                        sx={{ mt: 1 }}
                      />
                    )}
                  </Box>
                </Grid>

                {/* Row 3 Right - Warranty & Return Policy */}
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'right' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                      🛡️ Warranty & Returns
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      {product.warranty}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      🔄 {product.availability?.returnPolicy}
                    </Typography>

                    {/* Trust Badges */}
                    <Stack direction="row" spacing={1} justifyContent="flex-end" sx={{ mt: 1 }}>
                      <Chip
                        label="100% Genuine"
                        size="small"
                        icon={<Security />}
                      />
                      <Chip
                        label="Secure Payment"
                        size="small"
                        icon={<Security />}
                      />
                    </Stack>
                  </Box>
                </Grid>

                {/* Quantity Selector & Action Buttons */}
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    {/* Quantity Selector */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2">Qty:</Typography>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      >
                        -
                      </Button>
                      <Typography sx={{ minWidth: 30, textAlign: 'center' }}>
                        {quantity}
                      </Typography>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setQuantity(quantity + 1)}
                      >
                        +
                      </Button>
                    </Box>

                    {/* Action Buttons */}
                    <Box sx={{ display: 'flex', gap: 1, flex: 1 }}>
                      <Button
                        variant="contained"
                        startIcon={<ShoppingCart />}
                        sx={{ flex: 1 }}
                      >
                        Add to Cart
                      </Button>
                      <Button
                        variant="outlined"
                        onClick={() => onViewProduct(product)}
                        startIcon={<Visibility />}
                      >
                        View Details
                      </Button>
                      <IconButton
                        onClick={() => setIsFavorite(!isFavorite)}
                        color={isFavorite ? 'error' : 'default'}
                      >
                        {isFavorite ? <Favorite /> : <FavoriteBorder />}
                      </IconButton>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Grid>
        </Grid>

        {/* Bottom Section */}
        <Divider />
        <Grid container>
          {/* Bottom Left Block - Technical Specifications */}
          <Grid item xs={12} md={4}>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                🔧 Technical Specifications
              </Typography>
              <Grid container spacing={1}>
                <Grid item xs={6}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Weight:</Typography>
                  <Typography variant="body2">{product.technicalDetails?.weight}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Dimensions:</Typography>
                  <Typography variant="body2">{product.technicalDetails?.dimensions}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Material:</Typography>
                  <Typography variant="body2">{product.technicalDetails?.material}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Finish:</Typography>
                  <Typography variant="body2">{product.technicalDetails?.finish}</Typography>
                </Grid>
              </Grid>

              {/* Special Features */}
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mt: 2, mb: 1 }}>
                Special Features:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {product.technicalDetails?.specialFeatures?.map((feature, index) => (
                  <Chip key={index} label={feature} size="small" />
                ))}
              </Stack>
            </Box>
          </Grid>

          {/* Bottom Right Block - Installation & Contents */}
          <Grid item xs={12} md={4}>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                📦 Installation & Package
              </Typography>

              {/* Installation Method */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  Installation Method:
                </Typography>
                <Chip
                  label={product.technicalDetails?.installationType || 'Professional Required'}
                  color={product.technicalDetails?.installationType === 'DIY Friendly' ? 'success' : 'warning'}
                />
              </Box>

              {/* In the Box Contents */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                  In the Box:
                </Typography>
                <Stack spacing={0.5}>
                  <Typography variant="body2">• Main Product</Typography>
                  {product.variants?.accessories?.map((accessory, index) => (
                    <Typography key={index} variant="body2">• {accessory}</Typography>
                  ))}
                </Stack>
              </Box>

              {/* Compatibility Info */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  Compatibility:
                </Typography>
                <Typography variant="body2">
                  {product.fitment?.vehicleCompatibility}
                </Typography>
              </Box>

              {/* Certifications */}
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Certifications:
                </Typography>
                <Stack direction="row" spacing={1}>
                  <Chip label="ISO Certified" size="small" />
                  <Chip label="Quality Tested" size="small" />
                </Stack>
              </Box>
            </Box>
          </Grid>

          {/* Bottom Wide Block - Customer Reviews & Q&A */}
          <Grid item xs={12} md={4}>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                ⭐ Customer Reviews & Q&A
              </Typography>

              {/* Rating Summary */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Rating value={product.ratings?.average || 0} readOnly />
                <Typography variant="h6" sx={{ ml: 1 }}>
                  {product.ratings?.average}
                </Typography>
                <Typography variant="body2" sx={{ ml: 1, color: 'text.secondary' }}>
                  ({product.ratings?.totalReviews} reviews)
                </Typography>
              </Box>

              {/* Featured Review */}
              {product.userTrust?.featuredReviews?.[0] && (
                <Box sx={{ mb: 2, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Avatar sx={{ width: 24, height: 24, mr: 1 }}>
                      {product.userTrust.featuredReviews[0].user[0]}
                    </Avatar>
                    <Typography variant="subtitle2">
                      {product.userTrust.featuredReviews[0].user}
                    </Typography>
                    {product.userTrust.featuredReviews[0].verified && (
                      <Chip label="Verified" size="small" color="success" sx={{ ml: 1 }} />
                    )}
                  </Box>
                  <Rating value={product.userTrust.featuredReviews[0].rating} size="small" readOnly />
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    "{product.userTrust.featuredReviews[0].comment}"
                  </Typography>
                </Box>
              )}

              {/* Q&A Section */}
              {product.userTrust?.qnaSection?.[0] && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    Q&A:
                  </Typography>
                  <Box sx={{ p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                      Q: {product.userTrust.qnaSection[0].question}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      A: {product.userTrust.qnaSection[0].answer}
                    </Typography>
                  </Box>
                </Box>
              )}

              {/* User Ratings Filter */}
              <Box sx={{ mt: 2 }}>
                <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 1 }}>
                  Rating Distribution:
                </Typography>
                {[5, 4, 3, 2, 1].map((stars) => (
                  <Box key={stars} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                    <Typography variant="caption" sx={{ minWidth: 20 }}>
                      {stars}★
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={Math.random() * 100}
                      sx={{ flex: 1, mx: 1, height: 4 }}
                    />
                    <Typography variant="caption">
                      {Math.floor(Math.random() * 100)}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          </Grid>
        </Grid>

        {/* Optional Bottom Right - Add-ons & Recommendations */}
        <Divider />
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            🛍️ Frequently Bought Together & Recommendations
          </Typography>
          <Grid container spacing={2}>
            {/* Add-ons/Accessories */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                Add-ons & Accessories:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {product.variants?.accessories?.map((accessory, index) => (
                  <Chip
                    key={index}
                    label={accessory}
                    variant="outlined"
                    clickable
                  />
                ))}
              </Stack>
            </Grid>

            {/* Bundle Options */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                Bundle Offers:
              </Typography>
              <Chip
                label={product.variants?.bundleOptions || 'Bundle & Save'}
                color="primary"
                clickable
              />
            </Grid>
          </Grid>
        </Box>
      </Card>
    );
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Page Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 2 }}>
          Premium Aftermarket Products
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          High-performance automotive parts and accessories
        </Typography>
      </Box>

      {/* Product Cards */}
      <Stack spacing={4}>
        {products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </Stack>

      {/* Load More Button */}
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Button variant="outlined" size="large">
          Load More Products
        </Button>
      </Box>
    </Container>
  );
};

export default LandingPage;
