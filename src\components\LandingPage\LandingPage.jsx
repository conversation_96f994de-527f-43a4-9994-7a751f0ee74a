import {
  Box,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Button,
  Chip,
  Rating,
  Badge,
  IconButton,
  Divider,
  Grid,
  Container
} from '@mui/material';
import {
  FavoriteOutlined,
  ShareOutlined,
  ShoppingCartOutlined,
  LocalShippingOutlined,
  SecurityOutlined,
  VerifiedOutlined
} from '@mui/icons-material';
import './LandingPage.css';

const LandingPage = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4, overflowX: 'hidden' }}>
      <Grid container spacing={3}>
        {/* Product Card 1 */}
        <Grid item xs={12} sm={6} md={4}>
          <Card
            className="product-card"
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              transition: 'transform 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-8px)',
                boxShadow: '0 12px 24px rgba(0,0,0,0.15)'
              }
            }}
          >
            {/* Sale Badge */}
            <Badge
              badgeContent="25% OFF"
              color="error"
              sx={{
                position: 'absolute',
                top: 16,
                left: 16,
                zIndex: 1,
                '& .MuiBadge-badge': {
                  fontSize: '0.75rem',
                  fontWeight: 'bold'
                }
              }}
            />

            {/* Action Buttons */}
            <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1 }}>
              <IconButton size="small" sx={{ bgcolor: 'white', mb: 1, '&:hover': { color: 'red' } }}>
                <FavoriteOutlined />
              </IconButton>
              <IconButton size="small" sx={{ bgcolor: 'white', display: 'block' }}>
                <ShareOutlined />
              </IconButton>
            </Box>

            {/* Product Image */}
            <CardMedia
              component="img"
              height="250"
              image="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=250&fit=crop"
              alt="Premium Headphones"
              sx={{ objectFit: 'cover' }}
            />

            <CardContent sx={{ flexGrow: 1, p: 2 }}>
              {/* Brand & Verified */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="caption" color="primary" sx={{ fontWeight: 'bold' }}>
                  SONY
                </Typography>
                <VerifiedOutlined sx={{ ml: 0.5, fontSize: 16, color: 'primary.main' }} />
              </Box>

              {/* Product Title */}
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: 'bold',
                  mb: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}
              >
                Premium Wireless Noise-Cancelling Headphones WH-1000XM5
              </Typography>

              {/* Rating & Reviews */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Rating value={4.5} precision={0.5} size="small" readOnly />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  4.5 (2,847 reviews)
                </Typography>
              </Box>

              {/* Features */}
              <Box sx={{ mb: 2 }}>
                <Chip label="Wireless" size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                <Chip label="Noise Cancelling" size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                <Chip label="30H Battery" size="small" sx={{ mr: 0.5, mb: 0.5 }} />
              </Box>

              {/* Price */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="h5" component="span" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                  $299.99
                </Typography>
                <Typography
                  variant="body2"
                  component="span"
                  sx={{
                    textDecoration: 'line-through',
                    color: 'text.secondary',
                    ml: 1
                  }}
                >
                  $399.99
                </Typography>
              </Box>

              {/* Shipping Info */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocalShippingOutlined sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                <Typography variant="caption" color="success.main">
                  Free shipping • Arrives tomorrow
                </Typography>
              </Box>

              {/* Security Badge */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SecurityOutlined sx={{ fontSize: 16, color: 'primary.main', mr: 0.5 }} />
                <Typography variant="caption" color="text.secondary">
                  Secure payment • 2-year warranty
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<ShoppingCartOutlined />}
                  fullWidth
                  sx={{
                    bgcolor: 'primary.main',
                    '&:hover': { bgcolor: 'primary.dark' }
                  }}
                >
                  Add to Cart
                </Button>
                <Button
                  variant="outlined"
                  sx={{ minWidth: 'auto', px: 2 }}
                >
                  Buy Now
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Product Card 2 */}
        <Grid item xs={12} sm={6} md={4}>
          <Card
            className="product-card"
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              transition: 'transform 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-8px)',
                boxShadow: '0 12px 24px rgba(0,0,0,0.15)'
              }
            }}
          >
            {/* New Badge */}
            <Badge
              badgeContent="NEW"
              color="success"
              sx={{
                position: 'absolute',
                top: 16,
                left: 16,
                zIndex: 1,
                '& .MuiBadge-badge': {
                  fontSize: '0.75rem',
                  fontWeight: 'bold'
                }
              }}
            />

            {/* Action Buttons */}
            <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1 }}>
              <IconButton size="small" sx={{ bgcolor: 'white', mb: 1, '&:hover': { color: 'red' } }}>
                <FavoriteOutlined />
              </IconButton>
              <IconButton size="small" sx={{ bgcolor: 'white', display: 'block' }}>
                <ShareOutlined />
              </IconButton>
            </Box>

            {/* Product Image */}
            <CardMedia
              component="img"
              height="250"
              image="https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=250&fit=crop"
              alt="Running Shoes"
              sx={{ objectFit: 'cover' }}
            />

            <CardContent sx={{ flexGrow: 1, p: 2 }}>
              {/* Brand & Verified */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="caption" color="primary" sx={{ fontWeight: 'bold' }}>
                  NIKE
                </Typography>
                <VerifiedOutlined sx={{ ml: 0.5, fontSize: 16, color: 'primary.main' }} />
              </Box>

              {/* Product Title */}
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: 'bold',
                  mb: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}
              >
                Air Max 270 React Running Shoes - Men's
              </Typography>

              {/* Rating & Reviews */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Rating value={4.8} precision={0.1} size="small" readOnly />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  4.8 (1,234 reviews)
                </Typography>
              </Box>

              {/* Features */}
              <Box sx={{ mb: 2 }}>
                <Chip label="Breathable" size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                <Chip label="Lightweight" size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                <Chip label="Durable" size="small" sx={{ mr: 0.5, mb: 0.5 }} />
              </Box>

              {/* Price */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="h5" component="span" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                  $149.99
                </Typography>
              </Box>

              {/* Shipping Info */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocalShippingOutlined sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                <Typography variant="caption" color="success.main">
                  Free shipping • Arrives in 2-3 days
                </Typography>
              </Box>

              {/* Security Badge */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SecurityOutlined sx={{ fontSize: 16, color: 'primary.main', mr: 0.5 }} />
                <Typography variant="caption" color="text.secondary">
                  Secure payment • 1-year warranty
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<ShoppingCartOutlined />}
                  fullWidth
                  sx={{
                    bgcolor: 'primary.main',
                    '&:hover': { bgcolor: 'primary.dark' }
                  }}
                >
                  Add to Cart
                </Button>
                <Button
                  variant="outlined"
                  sx={{ minWidth: 'auto', px: 2 }}
                >
                  Buy Now
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Product Card 3 */}
        <Grid item xs={12} sm={6} md={4}>
          <Card
            className="product-card"
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              transition: 'transform 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-8px)',
                boxShadow: '0 12px 24px rgba(0,0,0,0.15)'
              }
            }}
          >
            {/* Best Seller Badge */}
            <Badge
              badgeContent="BEST SELLER"
              color="warning"
              sx={{
                position: 'absolute',
                top: 16,
                left: 16,
                zIndex: 1,
                '& .MuiBadge-badge': {
                  fontSize: '0.75rem',
                  fontWeight: 'bold'
                }
              }}
            />

            {/* Action Buttons */}
            <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1 }}>
              <IconButton size="small" sx={{ bgcolor: 'white', mb: 1, '&:hover': { color: 'red' } }}>
                <FavoriteOutlined />
              </IconButton>
              <IconButton size="small" sx={{ bgcolor: 'white', display: 'block' }}>
                <ShareOutlined />
              </IconButton>
            </Box>

            {/* Product Image */}
            <CardMedia
              component="img"
              height="250"
              image="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=250&fit=crop"
              alt="Smart Watch"
              sx={{ objectFit: 'cover' }}
            />

            <CardContent sx={{ flexGrow: 1, p: 2 }}>
              {/* Brand & Verified */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="caption" color="primary" sx={{ fontWeight: 'bold' }}>
                  APPLE
                </Typography>
                <VerifiedOutlined sx={{ ml: 0.5, fontSize: 16, color: 'primary.main' }} />
              </Box>

              {/* Product Title */}
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: 'bold',
                  mb: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}
              >
                Apple Watch Series 9 GPS + Cellular 45mm
              </Typography>

              {/* Rating & Reviews */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Rating value={4.9} precision={0.1} size="small" readOnly />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  4.9 (5,678 reviews)
                </Typography>
              </Box>

              {/* Features */}
              <Box sx={{ mb: 2 }}>
                <Chip label="GPS" size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                <Chip label="Cellular" size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                <Chip label="Health Tracking" size="small" sx={{ mr: 0.5, mb: 0.5 }} />
              </Box>

              {/* Price */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="h5" component="span" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                  $429.99
                </Typography>
                <Typography
                  variant="body2"
                  component="span"
                  sx={{
                    textDecoration: 'line-through',
                    color: 'text.secondary',
                    ml: 1
                  }}
                >
                  $499.99
                </Typography>
              </Box>

              {/* Shipping Info */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocalShippingOutlined sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                <Typography variant="caption" color="success.main">
                  Free shipping • Same day delivery
                </Typography>
              </Box>

              {/* Security Badge */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SecurityOutlined sx={{ fontSize: 16, color: 'primary.main', mr: 0.5 }} />
                <Typography variant="caption" color="text.secondary">
                  Secure payment • AppleCare+ available
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<ShoppingCartOutlined />}
                  fullWidth
                  sx={{
                    bgcolor: 'primary.main',
                    '&:hover': { bgcolor: 'primary.dark' }
                  }}
                >
                  Add to Cart
                </Button>
                <Button
                  variant="outlined"
                  sx={{ minWidth: 'auto', px: 2 }}
                >
                  Buy Now
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default LandingPage;