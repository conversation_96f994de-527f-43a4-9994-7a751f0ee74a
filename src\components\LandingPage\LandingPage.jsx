import {
  Box,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Button,
  Chip,
  Rating,
  Badge,
  IconButton,
  Divider,
  Grid,
  Container
} from '@mui/material';
// Icons will be added after installing @mui/icons-material
import './LandingPage.css';

// Aftermarket products data
const aftermarketProducts = [
  {
    id: 1,
    brand: 'PERFORMANCE PLUS',
    title: 'High-Performance Cold Air Intake System',
    image: 'data:image/jpeg;base64,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',
    rating: 4.7,
    reviews: 1234,
    price: 299.99,
    originalPrice: 399.99,
    features: ['+15 HP', 'Easy Install', 'Lifetime Warranty'],
    shipping: 'Free shipping • Arrives in 2-3 days',
    badge: 'PERFORMANCE',
    badgeColor: 'error',
    category: 'Engine Performance'
  }
];

const LandingPage = ({ onViewProduct }) => {
  return (
    <Container maxWidth="lg" sx={{ py: 4, overflowX: 'hidden' }}>
      {/* Page Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold', mb: 2 }}>
          Aftermarket Performance Parts
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Upgrade your vehicle with premium aftermarket components
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {aftermarketProducts.map((product) => (
          <Grid item xs={12} sm={6} md={4} key={product.id}>
            <Card
              className="product-card"
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                transition: 'transform 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 24px rgba(0,0,0,0.15)'
                }
              }}
            >
              {/* Badge */}
              <Badge
                badgeContent={product.badge}
                color={product.badgeColor}
                sx={{
                  position: 'absolute',
                  top: 16,
                  left: 16,
                  zIndex: 1,
                  '& .MuiBadge-badge': {
                    fontSize: '0.75rem',
                    fontWeight: 'bold'
                  }
                }}
              />

              {/* Action Buttons */}
              <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1 }}>
                <Button
                  size="small"
                  variant="contained"
                  sx={{ bgcolor: 'white', color: 'primary.main', mb: 1, '&:hover': { bgcolor: 'primary.main', color: 'white' } }}
                  onClick={() => onViewProduct(product)}
                >
                  👁️
                </Button>
              </Box>

              {/* Product Image */}
              <CardMedia
                component="img"
                height="250"
                image={product.image}
                alt={product.title}
                sx={{ objectFit: 'cover' }}
              />

              <CardContent sx={{ flexGrow: 1, p: 2 }}>
                {/* Brand & Category */}
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="caption" color="primary" sx={{ fontWeight: 'bold' }}>
                    {product.brand}
                  </Typography>
                  <Chip
                    label={product.category}
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                </Box>

                {/* Product Title */}
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    fontWeight: 'bold',
                    mb: 1,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}
                >
                  {product.title}
                </Typography>

                {/* Rating & Reviews */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Rating value={product.rating} precision={0.1} size="small" readOnly />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                    {product.rating} ({product.reviews.toLocaleString()} reviews)
                  </Typography>
                </Box>

                {/* Features */}
                <Box sx={{ mb: 2 }}>
                  {product.features.map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      size="small"
                      sx={{ mr: 0.5, mb: 0.5 }}
                    />
                  ))}
                </Box>

                {/* Price */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h5" component="span" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                    ${product.price}
                  </Typography>
                  {product.originalPrice && (
                    <Typography
                      variant="body2"
                      component="span"
                      sx={{
                        textDecoration: 'line-through',
                        color: 'text.secondary',
                        ml: 1
                      }}
                    >
                      ${product.originalPrice}
                    </Typography>
                  )}
                </Box>

                {/* Shipping Info */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="caption" color="success.main">
                    🚚 {product.shipping}
                  </Typography>
                </Box>

                {/* Security Badge */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    🔒 Secure payment • Warranty included
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* Action Buttons */}
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    fullWidth
                    sx={{
                      bgcolor: 'primary.main',
                      '&:hover': { bgcolor: 'primary.dark' }
                    }}
                  >
                    🛒 Add to Cart
                  </Button>
                  <Button
                    variant="outlined"
                    sx={{ minWidth: 'auto', px: 2 }}
                    onClick={() => onViewProduct(product)}
                  >
                    👁️ View
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
};

export default LandingPage;