import React from 'react';
import './LandingPage.css';
import Box from '../Box/Box.jsx';

const LandingPage = () => {
  return (
    <div className="landing-page">
      {/* Top Navigation Bar */}
      <Box className="nav-bar">
        <span>Navigation Bar</span>
      </Box>

      {/* Main Content Area */}
      <div className="main-content">
        {/* Left Sidebar */}
        <Box className="left-sidebar">
          <span>Left Sidebar</span>
        </Box>

        {/* Center Content */}
        <div className="center-content">
          {/* Top Section */}
          <div className="top-section">
            {/* Hero Section */}
            <Box className="hero-section">
              <span>Hero Section</span>
            </Box>

            {/* Right Grid */}
            <div className="right-grid">
              <Box className="grid-item">
                <span>Grid Item 1</span>
              </Box>
              <Box className="grid-item">
                <span>Grid Item 2</span>
              </Box>
              <Box className="grid-item">
                <span>Grid Item 3</span>
              </Box>
              <Box className="grid-item">
                <span>Grid Item 4</span>
              </Box>
              <Box className="grid-item">
                <span>Grid Item 5</span>
              </Box>
              <Box className="grid-item">
                <span>Grid Item 6</span>
              </Box>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="bottom-section">
            {/* Left Column */}
            <div className="left-column">
              <Box className="left-item">
                <span>Left Item 1</span>
              </Box>
              <Box className="left-item">
                <span>Left Item 2</span>
              </Box>
              <Box className="left-item">
                <span>Left Item 3</span>
              </Box>
            </div>

            {/* Right Column */}
            <div className="right-column">
              <Box className="right-item">
                <span>Right Item 1</span>
              </Box>
              <Box className="right-item">
                <span>Right Item 2</span>
              </Box>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
