.box {
  border: 2px solid var(--theme-border-color, #DCE6F0);
  background-color: var(--theme-background-secondary, #FFFFFF);
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family-primary, 'HCLTech Roobert', <PERSON><PERSON>, sans-serif);
  color: var(--color-text-default, #000032);
  font-size: var(--font-size-base, 1rem);
  transition: var(--transition-fast, 0.2s ease-in-out);
}

.box:hover {
  border-color: var(--color-primary-brand, #0F5FDC);
  box-shadow: var(--card-shadow, 0 4px 6px rgba(0, 0, 0, 0.1));
}
