.landing-page {
  width: 98vw;
  height: 95vh;
  display: flex;
  flex-direction: column;
  background-color: var(--theme-background-primary, #ECF3F8);
  font-family: var(--font-family-primary, 'HCLTech Roobert', <PERSON><PERSON>, sans-serif);
  margin:1%;
}

/* Navigation Bar */
.nav-bar {
  height: 60px;
  width: 100%;
  background-color: var(--navbar-bg, #0F5FDC);
  color: var(--navbar-text-color, #FFFFFF);
  border: 2px solid var(--theme-border-color, #DCE6F0);
  margin-bottom: 2px;
}

/* Main Content Area */
.main-content {
  display: flex;
  flex: 1;
  gap: 2px;
}

/* Left Sidebar */
.left-sidebar {
  width: 200px;
  height: 100%;
  flex-shrink: 0;
}

/* Center Content */
.center-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* Top Section */
.top-section {
  display: flex;
  gap: 2px;
  height: 60%;
}

/* Hero Section */
.hero-section {
  flex: 2;
  height: 100%;
}

/* Right Grid */
.right-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 2px;
  height: 100%;
}

.grid-item {
  min-height: 0;
}

/* Bottom Section */
.bottom-section {
  display: flex;
  gap: 2px;
  height: 40%;
}

/* Left Column */
.left-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.left-item {
  flex: 1;
}

/* Right Column */
.right-column {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.right-item {
  flex: 1;
}

/* Product Detail View Specific Styles */

/* Navigation Bar Content */
.nav-bar-content {
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-md);
  height: 100%;
}

.nav-bar-title {
  color: var(--navbar-text-color);
  flex: 1;
}

.nav-bar-sku {
  color: var(--navbar-text-color);
}

/* Left Sidebar - Image Gallery */
.image-gallery-container {
  padding: var(--spacing-sm);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.gallery-title {
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  text-align: center;
  font-size: 0.75rem;
}

.main-image-container {
  border: 2px solid var(--color-primary-brand);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-sm);
  padding: 1px;
}

.main-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
  border-radius: 4px;
}

.thumbnail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xs);
  flex: 1;
  align-content: start;
}

.thumbnail-image {
  width: 100%;
  height: 45px;
  object-fit: cover;
  border-radius: 3px;
  cursor: pointer;
  border: 1px solid var(--theme-border-color);
  opacity: 0.8;
  transition: var(--transition-fast);
}

.thumbnail-image.selected {
  border: 2px solid var(--color-primary-brand);
  opacity: 1;
}

.view-360-button {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
  font-size: 0.7rem;
}

/* Hero Section - Main Product Display */
.hero-container {
  padding: var(--spacing-sm);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.hero-image-container {
  height: 75%;
  margin-bottom: var(--spacing-sm);
  border: 1px solid var(--theme-border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  color: white;
  padding: var(--spacing-sm);
}

.overlay-text {
  font-size: 0.7rem;
  display: block;
}

.hero-info-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--spacing-sm);
  align-items: center;
}



.product-title {
  font-weight: var(--font-weight-bold);
  font-size: 0.9rem;
  line-height: 1.2;
  margin-bottom: var(--spacing-xs);
}

.product-rating-container {
  display: flex;
  align-items: center;
  margin-top: var(--spacing-xs);
}

.rating-text {
  margin-left: var(--spacing-xs);
  font-size: 0.7rem;
}

.product-price {
  font-weight: var(--font-weight-bold);
  color: var(--color-status-error);
  font-size: 1.1rem;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.quantity-button {
  min-width: auto;
  width: 24px;
  height: 24px;
  padding: 0;
}

.quantity-text {
  min-width: 20px;
  text-align: center;
  font-size: 0.8rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.compact-button {
  font-size: 0.7rem;
  padding: var(--spacing-xs) var(--spacing-md);
}

/* Right Grid Items */
.grid-item-content {
  padding: var(--spacing-xs);
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.grid-item-title {
  font-weight: var(--font-weight-bold);
  font-size: 0.65rem;
  margin-bottom: var(--spacing-xs);
}

.grid-item-value {
  color: var(--color-status-success);
  font-weight: var(--font-weight-bold);
  font-size: 1rem;
  line-height: 1;
}

.grid-item-subtitle {
  font-size: 0.6rem;
  margin-bottom: var(--spacing-xs);
}

.grid-item-progress {
  height: 3px;
}

.color-options {
  display: flex;
  justify-content: center;
  gap: 0.2rem;
  margin-bottom: var(--spacing-xs);
}

.color-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid var(--theme-border-color);
  cursor: pointer;
}

.size-options {
  display: flex;
  justify-content: center;
  gap: 0.15rem;
  margin-bottom: var(--spacing-xs);
}

.size-option {
  font-size: 0.6rem;
  padding: 0.1rem 0.2rem;
  border: 1px solid var(--theme-border-color);
  border-radius: 2px;
  min-width: 12px;
  text-align: center;
}

.rating-bars {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.rating-bar {
  height: 2px;
}

.compact-chip {
  font-size: 0.6rem;
  height: 16px;
}

/* Bottom Section Styles */
.left-item-content {
  padding: var(--spacing-sm);
  height: 100%;
  overflow: auto;
}

.section-title {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  font-size: 0.85rem;
}

/* Technical Specifications */
.specs-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xs);
  font-size: 0.75rem;
}



.spec-label {
  font-weight: var(--font-weight-bold);
  font-size: 0.7rem;
}

.spec-value {
  font-size: 0.65rem;
  display: block;
}

.features-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.2rem;
  margin-top: var(--spacing-xs);
}

.feature-chip {
  font-size: 0.6rem;
  height: 18px;
}

/* Customer Reviews */
.review-item {
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-xs);
  background-color: var(--theme-background-secondary);
  border-radius: 3px;
  border: 1px solid var(--theme-border-color);
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.15rem;
}

.review-avatar {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-sm);
  font-size: 0.6rem;
}

.review-user {
  font-weight: var(--font-weight-bold);
  font-size: 0.65rem;
}

.review-verified {
  margin-left: var(--spacing-sm);
  height: 12px;
  font-size: 0.5rem;
}

.review-rating {
  margin-left: auto;
  transform: scale(0.7);
}

.review-comment {
  font-size: 0.65rem;
  line-height: 1.3;
}

.review-stats {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-xs);
  background-color: var(--color-software-blue);
  border-radius: 3px;
}

.review-stats-text {
  font-size: 0.65rem;
  font-weight: var(--font-weight-bold);
}

/* FAQ Section */
.faq-item {
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-xs);
  border: 1px solid var(--theme-border-color);
  border-radius: 3px;
}

.faq-question {
  font-size: 0.7rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
  color: var(--color-primary-brand);
}

.faq-answer {
  font-size: 0.65rem;
  color: var(--color-text-secondary);
  line-height: 1.3;
}

.faq-actions {
  margin-top: var(--spacing-sm);
  display: flex;
  gap: var(--spacing-xs);
}

.faq-action-button {
  font-size: 0.6rem;
  padding: var(--spacing-xs) var(--spacing-sm);
}

/* Right Column Styles */
.right-item-content {
  padding: var(--spacing-md);
  height: 100%;
  overflow: auto;
}

/* Sales Analytics */
.sales-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.sales-stat {
  text-align: center;
  padding: var(--spacing-sm);
  border-radius: 4px;
}

.sales-stat.primary {
  background-color: var(--color-software-blue);
}

.sales-stat.success {
  background-color: #e8f5e8;
}

.sales-stat-value {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-brand);
}

.sales-stat.success .sales-stat-value {
  color: var(--color-status-success);
}

.sales-stat-label {
  font-size: 0.75rem;
}

.purchase-activity-title {
  font-size: 0.8rem;
  margin-bottom: var(--spacing-md);
  font-weight: var(--font-weight-bold);
}

.activity-item {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: 0.7rem;
}

.popularity-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.popularity-item {
  text-align: center;
}

.popularity-label {
  font-weight: var(--font-weight-bold);
  font-size: 0.75rem;
}

.popularity-bar {
  width: 60px;
  height: 4px;
}

.best-variants-title {
  font-size: 0.8rem;
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
}

.variant-chips {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* Shipping & Support */
.info-section {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  border-radius: 4px;
}

.info-section.shipping {
  background-color: #f0f8ff;
}

.info-section.warranty {
  background-color: #fff3e0;
}

.info-section-title {
  font-size: 0.8rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
}

.info-item {
  display: block;
  font-size: 0.75rem;
  margin-bottom: 0.1rem;
}

.support-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  margin-bottom: var(--spacing-md);
}

.support-button {
  font-size: 0.7rem;
  padding: var(--spacing-xs);
}



.guide-button {
  font-size: 0.7rem;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: var(--spacing-xs);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .left-sidebar {
    width: 100%;
    height: 100px;
  }

  .top-section {
    flex-direction: column;
    height: auto;
  }

  .right-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(6, 1fr);
  }

  .bottom-section {
    flex-direction: column;
  }

  .hero-info-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .thumbnail-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .specs-grid {
    grid-template-columns: 1fr;
  }

  .sales-grid {
    grid-template-columns: 1fr;
  }

  .popularity-metrics {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
