import { useState } from 'react';
import './App.css'
import LandingPage from './components/LandingPage/LandingPage.jsx';
import Productdetailview from './Product/Productdetailview.jsx';
import { ThemeProvider } from './contexts/ThemeContext.jsx';

function App() {
  const [currentView, setCurrentView] = useState('landing');
  const [selectedProduct, setSelectedProduct] = useState(null);

  const handleViewProduct = (product) => {
    setSelectedProduct(product);
    setCurrentView('productDetail');
  };

  const handleBackToLanding = () => {
    setCurrentView('landing');
    setSelectedProduct(null);
  };

  return (
    <ThemeProvider>
      {currentView === 'landing' ? (
        <LandingPage onViewProduct={handleViewProduct} />
      ) : (
        <Productdetailview
          product={selectedProduct}
          onBack={handleBackToLanding}
        />
      )}
    </ThemeProvider>
  )
}

export default App
