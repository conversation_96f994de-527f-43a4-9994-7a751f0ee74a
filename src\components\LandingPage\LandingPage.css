.landing-page {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--theme-background-primary, #ECF3F8);
  font-family: var(--font-family-primary, 'HCLTech Roobert', <PERSON><PERSON>, sans-serif);
}

/* Navigation Bar */
.nav-bar {
  height: 60px;
  width: 100%;
  background-color: var(--navbar-bg, #0F5FDC);
  color: var(--navbar-text-color, #FFFFFF);
  border: 2px solid var(--theme-border-color, #DCE6F0);
  margin-bottom: 2px;
}

/* Main Content Area */
.main-content {
  display: flex;
  flex: 1;
  gap: 2px;
}

/* Left Sidebar */
.left-sidebar {
  width: 200px;
  height: 100%;
  flex-shrink: 0;
}

/* Center Content */
.center-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* Top Section */
.top-section {
  display: flex;
  gap: 2px;
  height: 60%;
}

/* Hero Section */
.hero-section {
  flex: 2;
  height: 100%;
}

/* Right Grid */
.right-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 2px;
  height: 100%;
}

.grid-item {
  min-height: 0;
}

/* Bottom Section */
.bottom-section {
  display: flex;
  gap: 2px;
  height: 40%;
}

/* Left Column */
.left-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.left-item {
  flex: 1;
}

/* Right Column */
.right-column {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.right-item {
  flex: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-sidebar {
    width: 100%;
    height: 100px;
  }
  
  .top-section {
    flex-direction: column;
    height: auto;
  }
  
  .right-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(6, 1fr);
  }
  
  .bottom-section {
    flex-direction: column;
  }
}
