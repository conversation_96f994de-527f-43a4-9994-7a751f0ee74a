/* Landing Page Styles */
.product-card {
  border-radius: 16px !important;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.product-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15) !important;
}

/* Ensure no horizontal scroll */
body {
  overflow-x: hidden;
}

/* Custom badge styles */
.MuiBadge-badge {
  border-radius: 8px !important;
  padding: 4px 8px !important;
  font-size: 0.75rem !important;
  font-weight: bold !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Custom button styles */
.MuiButton-contained {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.MuiButton-outlined {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
  padding: 12px 16px !important;
}

/* Custom chip styles */
.MuiChip-root {
  border-radius: 8px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  background-color: rgba(25, 118, 210, 0.08) !important;
  color: #1976d2 !important;
}

/* Custom rating styles */
.MuiRating-root {
  color: #ff9800 !important;
}

/* Custom icon button styles */
.MuiIconButton-root {
  border-radius: 50% !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease-in-out !important;
}

.MuiIconButton-root:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Custom card media styles */
.MuiCardMedia-root {
  transition: transform 0.3s ease-in-out !important;
}

.product-card:hover .MuiCardMedia-root {
  transform: scale(1.05) !important;
}

/* Typography enhancements */
.MuiTypography-h6 {
  line-height: 1.3 !important;
}

.MuiTypography-caption {
  font-weight: 500 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .product-card {
    margin-bottom: 16px;
  }

  .MuiContainer-root {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
}

/* Animation for smooth loading */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-card {
  animation: fadeInUp 0.6s ease-out !important;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}