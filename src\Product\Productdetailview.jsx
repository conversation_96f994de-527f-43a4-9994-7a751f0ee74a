import React, { useState } from 'react';
import './ProductDetailview.css';
import Box from '../components/Box/Box.jsx';
import {
  Typography,
  Button,
  Chip,
  Rating,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Badge,
  IconButton
} from '@mui/material';

const ProductDetailView = ({ product, onBack }) => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedColor, setSelectedColor] = useState('');
  const [selectedSize, setSelectedSize] = useState('');

  if (!product) {
    return (
      <div className="landing-page">
        <Box className="nav-bar">
          <Typography variant="h6" sx={{ color: 'white', p: 2 }}>No Product Selected</Typography>
        </Box>
        <div style={{ padding: '2rem', textAlign: 'center' }}>
          <Button variant="contained" onClick={onBack}>
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="landing-page">
      {/* Top Navigation Bar */}
      <Box className="nav-bar">
        <div style={{ display: 'flex', alignItems: 'center', padding: '0 1rem', height: '100%' }}>
          <Button
            variant="text"
            onClick={onBack}
            sx={{ color: 'white', mr: 2 }}
          >
            ← Back to Products
          </Button>
          <Typography variant="h6" sx={{ color: 'white', flex: 1 }}>
            {product.brand} - {product.productName}
          </Typography>
          <Typography variant="body2" sx={{ color: 'white' }}>
            SKU: {product.modelNumber}
          </Typography>
        </div>
      </Box>

      {/* Main Content Area */}
      <div className="main-content">
        {/* Left Sidebar - Product Image Gallery & 360° Views */}
        <Box className="left-sidebar">
          <div style={{ padding: '0.5rem', height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Typography variant="caption" sx={{ mb: 0.5, fontWeight: 'bold', textAlign: 'center', fontSize: '0.75rem' }}>
              360° Views
            </Typography>

            {/* Main Selected Thumbnail - Larger */}
            <div style={{
              border: '2px solid #1976d2',
              borderRadius: '6px',
              marginBottom: '0.5rem',
              padding: '1px'
            }}>
              <img
                src={product.images[selectedImage]}
                alt={`${product.productName} main view`}
                style={{
                  width: '100%',
                  height: '160px',
                  objectFit: 'cover',
                  borderRadius: '4px'
                }}
              />
            </div>

            {/* All Image Thumbnails - Compact Grid */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '0.25rem',
              flex: 1,
              alignContent: 'start'
            }}>
              {product.images.map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={`View ${index + 1}`}
                  onClick={() => setSelectedImage(index)}
                  style={{
                    width: '100%',
                    height: '45px',
                    objectFit: 'cover',
                    borderRadius: '3px',
                    cursor: 'pointer',
                    border: selectedImage === index ? '2px solid #1976d2' : '1px solid #ddd',
                    opacity: selectedImage === index ? 1 : 0.8
                  }}
                />
              ))}
            </div>

            {/* 360° View Button - Compact */}
            <Button
              variant="outlined"
              size="small"
              fullWidth
              sx={{ mt: 0.5, py: 0.25, fontSize: '0.7rem' }}
            >
              🔄 360°
            </Button>
          </div>
        </Box>

        {/* Center Content */}
        <div className="center-content">
          {/* Top Section */}
          <div className="top-section">
            {/* Hero Section - Main Product Image & Purchase Options */}
            <Box className="hero-section">
              <div style={{ padding: '0.5rem', height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* Large Product Image - Maximized */}
                <div style={{
                  height: '75%',
                  marginBottom: '0.5rem',
                  border: '1px solid #ddd',
                  borderRadius: '6px',
                  overflow: 'hidden',
                  position: 'relative'
                }}>
                  <img
                    src={product.images[selectedImage]}
                    alt={product.productName}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                  {/* Image overlay with quick info */}
                  <div style={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                    color: 'white',
                    padding: '0.5rem'
                  }}>
                    <Typography variant="caption" sx={{ fontSize: '0.7rem', display: 'block' }}>
                      {product.brand} • {product.modelNumber}
                    </Typography>
                  </div>
                </div>

                {/* Compact Product Info & Purchase - Horizontal Layout */}
                <div style={{
                  flex: 1,
                  display: 'grid',
                  gridTemplateColumns: '1fr auto auto',
                  gap: '0.5rem',
                  alignItems: 'center'
                }}>
                  {/* Product Info */}
                  <div>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold', fontSize: '0.9rem', lineHeight: 1.2 }}>
                      {product.productName.length > 30 ? product.productName.substring(0, 30) + '...' : product.productName}
                    </Typography>
                    <div style={{ display: 'flex', alignItems: 'center', marginTop: '0.25rem' }}>
                      <Rating value={product.ratings.average} precision={0.1} size="small" readOnly />
                      <Typography variant="caption" sx={{ ml: 0.5, fontSize: '0.7rem' }}>
                        {product.ratings.average}
                      </Typography>
                    </div>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'error.main', fontSize: '1.1rem' }}>
                      ${product.pricing.offerPrice}
                    </Typography>
                  </div>

                  {/* Quantity Selector - Compact */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      sx={{ minWidth: 'auto', width: '24px', height: '24px', p: 0 }}
                    >
                      -
                    </Button>
                    <Typography sx={{ minWidth: 20, textAlign: 'center', fontSize: '0.8rem' }}>
                      {quantity}
                    </Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setQuantity(quantity + 1)}
                      sx={{ minWidth: 'auto', width: '24px', height: '24px', p: 0 }}
                    >
                      +
                    </Button>
                  </div>

                  {/* Action Buttons - Vertical Stack */}
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ fontSize: '0.7rem', py: 0.25, px: 1 }}
                    >
                      🛒 Cart
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ fontSize: '0.7rem', py: 0.25, px: 1 }}
                    >
                      💰 Buy
                    </Button>
                  </div>
                </div>
              </div>
            </Box>

            {/* Right Grid - Product Analytics & Variants */}
            <div className="right-grid">
              {/* Sales Analytics */}
              <Box className="grid-item">
                <div style={{ padding: '0.25rem', textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.65rem', mb: 0.25 }}>
                    📊 Sales
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'success.main', fontWeight: 'bold', fontSize: '1rem', lineHeight: 1 }}>
                    {product.salesData?.totalSold || '1.2K'}
                  </Typography>
                  <Typography variant="caption" sx={{ fontSize: '0.6rem', mb: 0.25 }}>
                    Sold
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={75}
                    sx={{ height: 3 }}
                  />
                </div>
              </Box>

              {/* Color Variants */}
              <Box className="grid-item">
                <div style={{ padding: '0.25rem', textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.65rem', mb: 0.25 }}>
                    🎨 Colors
                  </Typography>
                  <div style={{ display: 'flex', justifyContent: 'center', gap: '0.2rem', marginBottom: '0.25rem' }}>
                    {['#ff0000', '#0000ff', '#000000', '#c0c0c0'].map((color, index) => (
                      <div
                        key={index}
                        style={{
                          width: '12px',
                          height: '12px',
                          borderRadius: '50%',
                          backgroundColor: color,
                          border: '1px solid #ccc',
                          cursor: 'pointer'
                        }}
                      />
                    ))}
                  </div>
                  <Typography variant="caption" sx={{ fontSize: '0.6rem' }}>
                    4 Options
                  </Typography>
                </div>
              </Box>

              {/* Size Variants */}
              <Box className="grid-item">
                <div style={{ padding: '0.25rem', textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.65rem', mb: 0.25 }}>
                    📏 Sizes
                  </Typography>
                  <div style={{ display: 'flex', justifyContent: 'center', gap: '0.15rem', marginBottom: '0.25rem' }}>
                    {['S', 'M', 'L', 'XL'].map((size) => (
                      <div
                        key={size}
                        style={{
                          fontSize: '0.6rem',
                          padding: '0.1rem 0.2rem',
                          border: '1px solid #ccc',
                          borderRadius: '2px',
                          minWidth: '12px',
                          textAlign: 'center'
                        }}
                      >
                        {size}
                      </div>
                    ))}
                  </div>
                  <Typography variant="caption" sx={{ fontSize: '0.6rem' }}>
                    4 Sizes
                  </Typography>
                </div>
              </Box>

              {/* Stock Status */}
              <Box className="grid-item">
                <div style={{ padding: '0.25rem', textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.65rem', mb: 0.25 }}>
                    📦 Stock
                  </Typography>
                  <Chip
                    label={product.availability.stockStatus === 'In Stock' ? 'In Stock' : 'Low'}
                    color={product.availability.stockStatus === 'In Stock' ? 'success' : 'warning'}
                    size="small"
                    sx={{ fontSize: '0.6rem', height: '16px' }}
                  />
                  <Typography variant="caption" sx={{ fontSize: '0.6rem', mt: 0.25 }}>
                    2-3 Days
                  </Typography>
                </div>
              </Box>

              {/* Customer Rating Distribution */}
              <Box className="grid-item">
                <div style={{ padding: '0.25rem', textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.65rem', mb: 0.25 }}>
                    ⭐ Rating
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'warning.main', fontWeight: 'bold', fontSize: '1rem', lineHeight: 1 }}>
                    {product.ratings.average}
                  </Typography>
                  <Typography variant="caption" sx={{ fontSize: '0.6rem', mb: 0.25 }}>
                    {Math.floor(product.ratings.totalReviews/1000)}K Reviews
                  </Typography>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1px' }}>
                    <LinearProgress variant="determinate" value={90} sx={{ height: 2 }} />
                    <LinearProgress variant="determinate" value={70} sx={{ height: 2 }} />
                    <LinearProgress variant="determinate" value={50} sx={{ height: 2 }} />
                  </div>
                </div>
              </Box>

              {/* Warranty & Support */}
              <Box className="grid-item">
                <div style={{ padding: '0.25rem', textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.65rem', mb: 0.25 }}>
                    🛡️ Warranty
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.65rem', mb: 0.25, lineHeight: 1.2 }}>
                    {product.warranty.split(' ').slice(0, 2).join(' ')}
                  </Typography>
                  <Chip
                    label="24/7"
                    size="small"
                    color="primary"
                    sx={{ fontSize: '0.6rem', height: '16px' }}
                  />
                </div>
              </Box>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="bottom-section">
            {/* Left Column - Product Details & FAQs */}
            <div className="left-column">
              {/* Technical Specifications */}
              <Box className="left-item">
                <div style={{ padding: '0.5rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 0.5, fontSize: '0.85rem' }}>
                    🔧 Technical Specs
                  </Typography>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '0.25rem', fontSize: '0.75rem' }}>
                    <div>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.7rem' }}>Weight:</Typography>
                      <Typography variant="caption" sx={{ fontSize: '0.65rem', display: 'block' }}>{product.technicalDetails.weight}</Typography>
                    </div>
                    <div>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.7rem' }}>Size:</Typography>
                      <Typography variant="caption" sx={{ fontSize: '0.65rem', display: 'block' }}>{product.technicalDetails.dimensions}</Typography>
                    </div>
                    <div>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.7rem' }}>Material:</Typography>
                      <Typography variant="caption" sx={{ fontSize: '0.65rem', display: 'block' }}>{product.technicalDetails.material}</Typography>
                    </div>
                    <div>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.7rem' }}>Install:</Typography>
                      <Typography variant="caption" sx={{ fontSize: '0.65rem', display: 'block' }}>{product.technicalDetails.installationType}</Typography>
                    </div>
                  </div>

                  {/* Special Features - Compact */}
                  <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.7rem', mt: 0.5, display: 'block' }}>Features:</Typography>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.2rem', marginTop: '0.25rem' }}>
                    {product.technicalDetails.specialFeatures.slice(0, 4).map((feature, index) => (
                      <Chip
                        key={index}
                        label={feature}
                        size="small"
                        sx={{ fontSize: '0.6rem', height: '18px' }}
                      />
                    ))}
                  </div>
                </div>
              </Box>

              {/* Customer Reviews & Ratings */}
              <Box className="left-item">
                <div style={{ padding: '0.5rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 0.5, fontSize: '0.85rem' }}>
                    ⭐ Reviews ({product.ratings.totalReviews})
                  </Typography>
                  {product.userTrust.featuredReviews.slice(0, 3).map((review, index) => (
                    <div key={index} style={{ marginBottom: '0.5rem', padding: '0.25rem', backgroundColor: '#f8f9fa', borderRadius: '3px', border: '1px solid #e9ecef' }}>
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.15rem' }}>
                        <Avatar sx={{ width: 16, height: 16, mr: 0.5, fontSize: '0.6rem' }}>
                          {review.user[0]}
                        </Avatar>
                        <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.65rem' }}>
                          {review.user.split(' ')[0]}
                        </Typography>
                        {review.verified && (
                          <Chip label="✓" size="small" color="success" sx={{ ml: 0.5, height: '12px', fontSize: '0.5rem' }} />
                        )}
                        <Rating value={review.rating} size="small" readOnly sx={{ ml: 'auto', transform: 'scale(0.7)' }} />
                      </div>
                      <Typography variant="body2" sx={{ fontSize: '0.65rem', lineHeight: 1.3 }}>
                        "{review.comment.length > 80 ? review.comment.substring(0, 80) + '...' : review.comment}"
                      </Typography>
                    </div>
                  ))}

                  {/* Quick Stats */}
                  <div style={{ marginTop: '0.5rem', padding: '0.25rem', backgroundColor: '#e3f2fd', borderRadius: '3px' }}>
                    <Typography variant="caption" sx={{ fontSize: '0.65rem', fontWeight: 'bold' }}>
                      Avg: {product.ratings.average}★ | {product.ratings.verifiedBuyers} Verified
                    </Typography>
                  </div>
                </div>
              </Box>

              {/* FAQs */}
              <Box className="left-item">
                <div style={{ padding: '0.5rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 0.5, fontSize: '0.85rem' }}>
                    ❓ FAQs
                  </Typography>
                  {product.userTrust.qnaSection.slice(0, 4).map((qa, index) => (
                    <div key={index} style={{ marginBottom: '0.5rem', padding: '0.25rem', border: '1px solid #e0e0e0', borderRadius: '3px' }}>
                      <Typography variant="body2" sx={{ fontSize: '0.7rem', fontWeight: 'bold', mb: 0.25, color: 'primary.main' }}>
                        Q: {qa.question.length > 50 ? qa.question.substring(0, 50) + '...' : qa.question}
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.65rem', color: 'text.secondary', lineHeight: 1.3 }}>
                        A: {qa.answer.length > 80 ? qa.answer.substring(0, 80) + '...' : qa.answer}
                      </Typography>
                    </div>
                  ))}

                  {/* Quick Links */}
                  <div style={{ marginTop: '0.5rem', display: 'flex', gap: '0.25rem' }}>
                    <Button variant="text" size="small" sx={{ fontSize: '0.6rem', py: 0.25, px: 0.5 }}>
                      📄 Manual
                    </Button>
                    <Button variant="text" size="small" sx={{ fontSize: '0.6rem', py: 0.25, px: 0.5 }}>
                      🎥 Video
                    </Button>
                    <Button variant="text" size="small" sx={{ fontSize: '0.6rem', py: 0.25, px: 0.5 }}>
                      💬 Ask
                    </Button>
                  </div>
                </div>
              </Box>
            </div>

            {/* Right Column - Purchase Analytics & Related Info */}
            <div className="right-column">
              {/* Sales Graph & Purchase Analytics */}
              <Box className="right-item">
                <div style={{ padding: '1rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    📈 Sales Analytics & Purchase Trends
                  </Typography>

                  {/* Sales Numbers */}
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1rem' }}>
                    <div style={{ textAlign: 'center', padding: '0.5rem', backgroundColor: '#e3f2fd', borderRadius: '4px' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                        {product.salesData?.totalSold || '1,234'}
                      </Typography>
                      <Typography variant="caption">Total Sold</Typography>
                    </div>
                    <div style={{ textAlign: 'center', padding: '0.5rem', backgroundColor: '#e8f5e8', borderRadius: '4px' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                        {product.salesData?.thisMonth || '156'}
                      </Typography>
                      <Typography variant="caption">This Month</Typography>
                    </div>
                  </div>

                  {/* Purchase Trends */}
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', mb: 1, fontWeight: 'bold' }}>
                    Recent Purchase Activity:
                  </Typography>
                  <div style={{ marginBottom: '1rem' }}>
                    {['John D. bought 2 hours ago', 'Sarah M. bought 5 hours ago', 'Mike R. bought 1 day ago'].map((activity, index) => (
                      <Typography key={index} variant="caption" sx={{ display: 'block', mb: 0.25, fontSize: '0.7rem' }}>
                        🛒 {activity}
                      </Typography>
                    ))}
                  </div>

                  {/* Popularity Metrics */}
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>
                    <div>
                      <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Popularity</Typography>
                      <LinearProgress variant="determinate" value={85} sx={{ width: 60, height: 4 }} />
                    </div>
                    <div>
                      <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Demand</Typography>
                      <LinearProgress variant="determinate" value={92} sx={{ width: 60, height: 4 }} />
                    </div>
                  </div>

                  {/* Best Selling Variants */}
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', mb: 0.5, fontWeight: 'bold' }}>
                    🏆 Best Selling Variants:
                  </Typography>
                  <div style={{ display: 'flex', gap: '0.25rem', flexWrap: 'wrap' }}>
                    <Chip label="Black - L" size="small" color="primary" />
                    <Chip label="Blue - M" size="small" color="primary" />
                    <Chip label="Red - XL" size="small" color="primary" />
                  </div>
                </div>
              </Box>

              {/* Shipping, Warranty & Support */}
              <Box className="right-item">
                <div style={{ padding: '1rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    🚚 Shipping & Support Information
                  </Typography>

                  {/* Shipping Details */}
                  <div style={{ marginBottom: '1rem', padding: '0.5rem', backgroundColor: '#f0f8ff', borderRadius: '4px' }}>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold', mb: 0.5 }}>
                      Shipping Options:
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      ✅ {product.availability.freeShipping ? 'Free Shipping' : 'Paid Shipping'}
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      📦 Delivery: {product.availability.estimatedDelivery}
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      💰 {product.availability.codAvailable ? 'COD Available' : 'Online Payment Only'}
                    </Typography>
                  </div>

                  {/* Warranty & Returns */}
                  <div style={{ marginBottom: '1rem', padding: '0.5rem', backgroundColor: '#fff3e0', borderRadius: '4px' }}>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold', mb: 0.5 }}>
                      Warranty & Returns:
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      🛡️ {product.warranty}
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      🔄 {product.availability.returnPolicy}
                    </Typography>
                  </div>

                  {/* Support Options */}
                  <div style={{ marginBottom: '1rem' }}>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold', mb: 0.5 }}>
                      Customer Support:
                    </Typography>
                    <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                      <Button variant="outlined" size="small" sx={{ fontSize: '0.7rem', py: 0.25 }}>
                        💬 Live Chat
                      </Button>
                      <Button variant="outlined" size="small" sx={{ fontSize: '0.7rem', py: 0.25 }}>
                        📞 Call Support
                      </Button>
                      <Button variant="outlined" size="small" sx={{ fontSize: '0.7rem', py: 0.25 }}>
                        📧 Email
                      </Button>
                    </div>
                  </div>

                  {/* Installation Guide */}
                  <div>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold', mb: 0.5 }}>
                      Installation & Guides:
                    </Typography>
                    <Button variant="text" size="small" fullWidth sx={{ fontSize: '0.7rem', justifyContent: 'flex-start' }}>
                      📄 Download PDF Guide
                    </Button>
                    <Button variant="text" size="small" fullWidth sx={{ fontSize: '0.7rem', justifyContent: 'flex-start' }}>
                      🎥 Watch Video Tutorial
                    </Button>
                  </div>
                </div>
              </Box>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailView;
