import React from 'react';
import './ProductDetailview.css';
import Box from '../components/Box/Box.jsx';
import { Typography, Button, Chip, Rating } from '@mui/material';

const ProductDetailView = ({ product, onBack }) => {
  if (!product) {
    return (
      <div className="landing-page">
        <Box className="nav-bar">
          <Typography variant="h6" sx={{ color: 'white' }}>No Product Selected</Typography>
        </Box>
        <div style={{ padding: '2rem', textAlign: 'center' }}>
          <Button variant="contained" onClick={onBack}>
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="landing-page">
      {/* Top Navigation Bar */}
      <Box className="nav-bar">
        <Button
          variant="text"
          onClick={onBack}
          sx={{ color: 'white', mr: 2 }}
        >
          ← Back
        </Button>
        <Typography variant="h6" sx={{ color: 'white' }}>
          {product.brand} - {product.productName}
        </Typography>
      </Box>

      {/* Main Content Area */}
      <div className="main-content">
        {/* Left Sidebar - Product Images */}
        <Box className="left-sidebar">
          <div style={{ padding: '1rem', height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
              Product Images
            </Typography>
            <img
              src={product.images[0]}
              alt={product.productName}
              style={{
                width: '100%',
                height: '150px',
                objectFit: 'cover',
                borderRadius: '4px',
                marginBottom: '0.5rem'
              }}
            />
            {product.images.slice(1, 3).map((image, index) => (
              <img
                key={index}
                src={image}
                alt={`${product.productName} view ${index + 2}`}
                style={{
                  width: '100%',
                  height: '60px',
                  objectFit: 'cover',
                  borderRadius: '4px',
                  marginBottom: '0.5rem'
                }}
              />
            ))}
          </div>
        </Box>

        {/* Center Content */}
        <div className="center-content">
          {/* Top Section */}
          <div className="top-section">
            {/* Hero Section - Main Product Info */}
            <Box className="hero-section">
              <div style={{ padding: '1rem', height: '100%', overflow: 'auto' }}>
                <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {product.productName}
                </Typography>
                <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
                  {product.shortDescription}
                </Typography>

                <div style={{ marginBottom: '1rem' }}>
                  <Rating value={product.ratings.average} precision={0.1} size="small" readOnly />
                  <Typography variant="caption" sx={{ ml: 1 }}>
                    {product.ratings.average} ({product.ratings.totalReviews} reviews)
                  </Typography>
                </div>

                <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'error.main', mb: 1 }}>
                  ${product.pricing.offerPrice}
                </Typography>
                {product.pricing.retailPrice !== product.pricing.offerPrice && (
                  <Typography variant="body2" sx={{ textDecoration: 'line-through', color: 'text.secondary' }}>
                    ${product.pricing.retailPrice}
                  </Typography>
                )}

                <div style={{ marginTop: '1rem' }}>
                  <Chip
                    label={product.availability.stockStatus}
                    color={product.availability.stockStatus === 'In Stock' ? 'success' : 'warning'}
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  <Chip label={product.warranty} size="small" />
                </div>
              </div>
            </Box>

            {/* Right Grid - Product Details */}
            <div className="right-grid">
              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                    Vehicle Compatibility
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.fitment.vehicleCompatibility}
                  </Typography>
                </div>
              </Box>

              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                    Weight & Size
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.technicalDetails.weight}
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.technicalDetails.dimensions}
                  </Typography>
                </div>
              </Box>

              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                    Material
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.technicalDetails.material}
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.technicalDetails.finish}
                  </Typography>
                </div>
              </Box>

              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                    Installation
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.technicalDetails.installationType}
                  </Typography>
                </div>
              </Box>

              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                    Shipping
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.availability.freeShipping ? 'Free Shipping' : 'Paid Shipping'}
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.availability.estimatedDelivery}
                  </Typography>
                </div>
              </Box>

              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                    Payment
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.availability.codAvailable ? 'COD Available' : 'Online Only'}
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                    {product.pricing.emiOptions}
                  </Typography>
                </div>
              </Box>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="bottom-section">
            {/* Left Column - Features & Specifications */}
            <div className="left-column">
              <Box className="left-item">
                <div style={{ padding: '1rem', height: '100%' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    Special Features
                  </Typography>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.25rem' }}>
                    {product.technicalDetails.specialFeatures.map((feature, index) => (
                      <Chip key={index} label={feature} size="small" />
                    ))}
                  </div>
                </div>
              </Box>

              <Box className="left-item">
                <div style={{ padding: '1rem', height: '100%' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    Customer Reviews
                  </Typography>
                  {product.userTrust.featuredReviews.slice(0, 1).map((review, index) => (
                    <div key={index}>
                      <Rating value={review.rating} size="small" readOnly />
                      <Typography variant="body2" sx={{ fontSize: '0.8rem', mt: 0.5 }}>
                        "{review.comment}"
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                        - {review.user}
                      </Typography>
                    </div>
                  ))}
                </div>
              </Box>

              <Box className="left-item">
                <div style={{ padding: '1rem', height: '100%' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    Support & Warranty
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', mb: 1 }}>
                    {product.warranty}
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                    {product.availability.returnPolicy}
                  </Typography>
                </div>
              </Box>
            </div>

            {/* Right Column - Purchase Actions & Q&A */}
            <div className="right-column">
              <Box className="right-item">
                <div style={{ padding: '1rem', height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 2 }}>
                    Purchase Options
                  </Typography>

                  <div style={{ marginBottom: '1rem' }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>Quantity:</Typography>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <Button variant="outlined" size="small">-</Button>
                      <Typography>1</Typography>
                      <Button variant="outlined" size="small">+</Button>
                    </div>
                  </div>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', flex: 1 }}>
                    <Button variant="contained" fullWidth>
                      🛒 Add to Cart
                    </Button>
                    <Button variant="outlined" fullWidth>
                      💰 Buy Now
                    </Button>
                    <Button variant="text" fullWidth>
                      ❤️ Wishlist
                    </Button>
                  </div>
                </div>
              </Box>

              <Box className="right-item">
                <div style={{ padding: '1rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    Questions & Answers
                  </Typography>
                  {product.userTrust.qnaSection.slice(0, 2).map((qa, index) => (
                    <div key={index} style={{ marginBottom: '1rem' }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.8rem' }}>
                        Q: {qa.question}
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                        A: {qa.answer}
                      </Typography>
                    </div>
                  ))}
                </div>
              </Box>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailView;
