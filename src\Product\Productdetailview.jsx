import React, { useState } from 'react';
import './ProductDetailview.css';
import Box from '../components/Box/Box.jsx';
import {
  Typography,
  Button,
  Chip,
  Rating,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Badge,
  IconButton
} from '@mui/material';

const ProductDetailView = ({ product, onBack }) => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedColor, setSelectedColor] = useState('');
  const [selectedSize, setSelectedSize] = useState('');

  if (!product) {
    return (
      <div className="landing-page">
        <Box className="nav-bar">
          <Typography variant="h6" sx={{ color: 'white', p: 2 }}>No Product Selected</Typography>
        </Box>
        <div style={{ padding: '2rem', textAlign: 'center' }}>
          <Button variant="contained" onClick={onBack}>
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="landing-page">
      {/* Top Navigation Bar */}
      <Box className="nav-bar">
        <div style={{ display: 'flex', alignItems: 'center', padding: '0 1rem', height: '100%' }}>
          <Button
            variant="text"
            onClick={onBack}
            sx={{ color: 'white', mr: 2 }}
          >
            ← Back to Products
          </Button>
          <Typography variant="h6" sx={{ color: 'white', flex: 1 }}>
            {product.brand} - {product.productName}
          </Typography>
          <Typography variant="body2" sx={{ color: 'white' }}>
            SKU: {product.modelNumber}
          </Typography>
        </div>
      </Box>

      {/* Main Content Area */}
      <div className="main-content">
        {/* Left Sidebar - Product Image Gallery & 360° Views */}
        <Box className="left-sidebar">
          <div style={{ padding: '1rem', height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold', textAlign: 'center' }}>
              360° Product Views
            </Typography>

            {/* Main Selected Thumbnail */}
            <div style={{
              border: '3px solid #1976d2',
              borderRadius: '8px',
              marginBottom: '1rem',
              padding: '2px'
            }}>
              <img
                src={product.images[selectedImage]}
                alt={`${product.productName} main view`}
                style={{
                  width: '100%',
                  height: '120px',
                  objectFit: 'cover',
                  borderRadius: '6px'
                }}
              />
            </div>

            {/* All Image Thumbnails */}
            <div style={{ flex: 1, overflowY: 'auto' }}>
              {product.images.map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={`${product.productName} view ${index + 1}`}
                  onClick={() => setSelectedImage(index)}
                  style={{
                    width: '100%',
                    height: '60px',
                    objectFit: 'cover',
                    borderRadius: '4px',
                    marginBottom: '0.5rem',
                    cursor: 'pointer',
                    border: selectedImage === index ? '2px solid #1976d2' : '2px solid transparent',
                    opacity: selectedImage === index ? 1 : 0.7
                  }}
                />
              ))}
            </div>

            {/* 360° View Button */}
            <Button
              variant="outlined"
              size="small"
              fullWidth
              sx={{ mt: 1 }}
            >
              🔄 360° View
            </Button>
          </div>
        </Box>

        {/* Center Content */}
        <div className="center-content">
          {/* Top Section */}
          <div className="top-section">
            {/* Hero Section - Main Product Image & Purchase Options */}
            <Box className="hero-section">
              <div style={{ padding: '1rem', height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* Large Product Image */}
                <div style={{
                  height: '60%',
                  marginBottom: '1rem',
                  border: '1px solid #ddd',
                  borderRadius: '8px',
                  overflow: 'hidden'
                }}>
                  <img
                    src={product.images[selectedImage]}
                    alt={product.productName}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                </div>

                {/* Product Info & Purchase */}
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                    {product.productName}
                  </Typography>

                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                    <Rating value={product.ratings.average} precision={0.1} size="small" readOnly />
                    <Typography variant="caption" sx={{ ml: 1 }}>
                      {product.ratings.average} ({product.ratings.totalReviews})
                    </Typography>
                  </div>

                  {/* Pricing */}
                  <div style={{ marginBottom: '1rem' }}>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                      ${product.pricing.offerPrice}
                    </Typography>
                    {product.pricing.retailPrice !== product.pricing.offerPrice && (
                      <Typography variant="body2" sx={{ textDecoration: 'line-through', color: 'text.secondary' }}>
                        ${product.pricing.retailPrice}
                      </Typography>
                    )}
                  </div>

                  {/* Quantity & Add to Cart */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    >
                      -
                    </Button>
                    <Typography sx={{ minWidth: 30, textAlign: 'center' }}>
                      {quantity}
                    </Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setQuantity(quantity + 1)}
                    >
                      +
                    </Button>
                  </div>

                  <Button variant="contained" fullWidth sx={{ mb: 1 }}>
                    🛒 Add to Cart
                  </Button>
                  <Button variant="outlined" fullWidth>
                    💰 Buy Now
                  </Button>
                </div>
              </div>
            </Box>

            {/* Right Grid - Product Analytics & Variants */}
            <div className="right-grid">
              {/* Sales Analytics */}
              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center', height: '100%' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
                    📊 Sales Analytics
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'success.main', fontWeight: 'bold' }}>
                    {product.salesData?.totalSold || '1,234'}
                  </Typography>
                  <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                    Units Sold
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={75}
                    sx={{ mt: 0.5, height: 4 }}
                  />
                </div>
              </Box>

              {/* Color Variants */}
              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center', height: '100%' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
                    🎨 Colors Available
                  </Typography>
                  <div style={{ display: 'flex', justifyContent: 'center', gap: '0.25rem', flexWrap: 'wrap' }}>
                    {['Red', 'Blue', 'Black', 'Silver'].map((color) => (
                      <div
                        key={color}
                        style={{
                          width: '20px',
                          height: '20px',
                          borderRadius: '50%',
                          backgroundColor: color.toLowerCase(),
                          border: '1px solid #ccc',
                          cursor: 'pointer'
                        }}
                        title={color}
                      />
                    ))}
                  </div>
                  <Typography variant="caption" sx={{ fontSize: '0.7rem', mt: 0.5, display: 'block' }}>
                    4 Colors
                  </Typography>
                </div>
              </Box>

              {/* Size Variants */}
              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center', height: '100%' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
                    📏 Size Options
                  </Typography>
                  <div style={{ display: 'flex', justifyContent: 'center', gap: '0.25rem', flexWrap: 'wrap' }}>
                    {['S', 'M', 'L', 'XL'].map((size) => (
                      <Chip
                        key={size}
                        label={size}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: '20px' }}
                      />
                    ))}
                  </div>
                  <Typography variant="caption" sx={{ fontSize: '0.7rem', mt: 0.5, display: 'block' }}>
                    4 Sizes
                  </Typography>
                </div>
              </Box>

              {/* Stock Status */}
              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center', height: '100%' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
                    📦 Stock Status
                  </Typography>
                  <Chip
                    label={product.availability.stockStatus}
                    color={product.availability.stockStatus === 'In Stock' ? 'success' : 'warning'}
                    size="small"
                  />
                  <Typography variant="caption" sx={{ fontSize: '0.7rem', mt: 0.5, display: 'block' }}>
                    {product.availability.estimatedDelivery}
                  </Typography>
                </div>
              </Box>

              {/* Customer Rating Distribution */}
              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center', height: '100%' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
                    ⭐ Rating Breakdown
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'warning.main', fontWeight: 'bold' }}>
                    {product.ratings.average}
                  </Typography>
                  <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                    {product.ratings.totalReviews} Reviews
                  </Typography>
                  <div style={{ marginTop: '0.25rem' }}>
                    <LinearProgress variant="determinate" value={90} sx={{ height: 3, mb: 0.25 }} />
                    <LinearProgress variant="determinate" value={70} sx={{ height: 3, mb: 0.25 }} />
                    <LinearProgress variant="determinate" value={50} sx={{ height: 3 }} />
                  </div>
                </div>
              </Box>

              {/* Warranty & Support */}
              <Box className="grid-item">
                <div style={{ padding: '0.5rem', textAlign: 'center', height: '100%' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
                    🛡️ Warranty
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.75rem', mb: 0.5 }}>
                    {product.warranty}
                  </Typography>
                  <Chip label="24/7 Support" size="small" color="primary" />
                </div>
              </Box>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="bottom-section">
            {/* Left Column - Product Details & FAQs */}
            <div className="left-column">
              {/* Technical Specifications */}
              <Box className="left-item">
                <div style={{ padding: '1rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    🔧 Technical Specifications
                  </Typography>
                  <List dense>
                    <ListItem sx={{ py: 0.25 }}>
                      <ListItemText
                        primary="Weight"
                        secondary={product.technicalDetails.weight}
                        primaryTypographyProps={{ fontSize: '0.8rem' }}
                        secondaryTypographyProps={{ fontSize: '0.75rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.25 }}>
                      <ListItemText
                        primary="Dimensions"
                        secondary={product.technicalDetails.dimensions}
                        primaryTypographyProps={{ fontSize: '0.8rem' }}
                        secondaryTypographyProps={{ fontSize: '0.75rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.25 }}>
                      <ListItemText
                        primary="Material"
                        secondary={product.technicalDetails.material}
                        primaryTypographyProps={{ fontSize: '0.8rem' }}
                        secondaryTypographyProps={{ fontSize: '0.75rem' }}
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.25 }}>
                      <ListItemText
                        primary="Installation"
                        secondary={product.technicalDetails.installationType}
                        primaryTypographyProps={{ fontSize: '0.8rem' }}
                        secondaryTypographyProps={{ fontSize: '0.75rem' }}
                      />
                    </ListItem>
                  </List>
                </div>
              </Box>

              {/* Customer Reviews & Ratings */}
              <Box className="left-item">
                <div style={{ padding: '1rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    ⭐ Customer Reviews
                  </Typography>
                  {product.userTrust.featuredReviews.slice(0, 2).map((review, index) => (
                    <div key={index} style={{ marginBottom: '1rem', padding: '0.5rem', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.25rem' }}>
                        <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: '0.8rem' }}>
                          {review.user[0]}
                        </Avatar>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                          {review.user}
                        </Typography>
                        {review.verified && (
                          <Chip label="✓" size="small" color="success" sx={{ ml: 1, height: '16px' }} />
                        )}
                      </div>
                      <Rating value={review.rating} size="small" readOnly sx={{ mb: 0.5 }} />
                      <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                        "{review.comment}"
                      </Typography>
                    </div>
                  ))}
                </div>
              </Box>

              {/* FAQs */}
              <Box className="left-item">
                <div style={{ padding: '1rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    ❓ Frequently Asked Questions
                  </Typography>
                  {product.userTrust.qnaSection.slice(0, 3).map((qa, index) => (
                    <Accordion key={index} sx={{ mb: 0.5 }}>
                      <AccordionSummary sx={{ minHeight: 'auto', py: 0.5 }}>
                        <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold' }}>
                          {qa.question}
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails sx={{ py: 0.5 }}>
                        <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
                          {qa.answer}
                        </Typography>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </div>
              </Box>
            </div>

            {/* Right Column - Purchase Analytics & Related Info */}
            <div className="right-column">
              {/* Sales Graph & Purchase Analytics */}
              <Box className="right-item">
                <div style={{ padding: '1rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    📈 Sales Analytics & Purchase Trends
                  </Typography>

                  {/* Sales Numbers */}
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1rem' }}>
                    <div style={{ textAlign: 'center', padding: '0.5rem', backgroundColor: '#e3f2fd', borderRadius: '4px' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                        {product.salesData?.totalSold || '1,234'}
                      </Typography>
                      <Typography variant="caption">Total Sold</Typography>
                    </div>
                    <div style={{ textAlign: 'center', padding: '0.5rem', backgroundColor: '#e8f5e8', borderRadius: '4px' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                        {product.salesData?.thisMonth || '156'}
                      </Typography>
                      <Typography variant="caption">This Month</Typography>
                    </div>
                  </div>

                  {/* Purchase Trends */}
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', mb: 1, fontWeight: 'bold' }}>
                    Recent Purchase Activity:
                  </Typography>
                  <div style={{ marginBottom: '1rem' }}>
                    {['John D. bought 2 hours ago', 'Sarah M. bought 5 hours ago', 'Mike R. bought 1 day ago'].map((activity, index) => (
                      <Typography key={index} variant="caption" sx={{ display: 'block', mb: 0.25, fontSize: '0.7rem' }}>
                        🛒 {activity}
                      </Typography>
                    ))}
                  </div>

                  {/* Popularity Metrics */}
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>
                    <div>
                      <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Popularity</Typography>
                      <LinearProgress variant="determinate" value={85} sx={{ width: 60, height: 4 }} />
                    </div>
                    <div>
                      <Typography variant="caption" sx={{ fontWeight: 'bold' }}>Demand</Typography>
                      <LinearProgress variant="determinate" value={92} sx={{ width: 60, height: 4 }} />
                    </div>
                  </div>

                  {/* Best Selling Variants */}
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', mb: 0.5, fontWeight: 'bold' }}>
                    🏆 Best Selling Variants:
                  </Typography>
                  <div style={{ display: 'flex', gap: '0.25rem', flexWrap: 'wrap' }}>
                    <Chip label="Black - L" size="small" color="primary" />
                    <Chip label="Blue - M" size="small" color="primary" />
                    <Chip label="Red - XL" size="small" color="primary" />
                  </div>
                </div>
              </Box>

              {/* Shipping, Warranty & Support */}
              <Box className="right-item">
                <div style={{ padding: '1rem', height: '100%', overflow: 'auto' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    🚚 Shipping & Support Information
                  </Typography>

                  {/* Shipping Details */}
                  <div style={{ marginBottom: '1rem', padding: '0.5rem', backgroundColor: '#f0f8ff', borderRadius: '4px' }}>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold', mb: 0.5 }}>
                      Shipping Options:
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      ✅ {product.availability.freeShipping ? 'Free Shipping' : 'Paid Shipping'}
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      📦 Delivery: {product.availability.estimatedDelivery}
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      💰 {product.availability.codAvailable ? 'COD Available' : 'Online Payment Only'}
                    </Typography>
                  </div>

                  {/* Warranty & Returns */}
                  <div style={{ marginBottom: '1rem', padding: '0.5rem', backgroundColor: '#fff3e0', borderRadius: '4px' }}>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold', mb: 0.5 }}>
                      Warranty & Returns:
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      🛡️ {product.warranty}
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', fontSize: '0.75rem' }}>
                      🔄 {product.availability.returnPolicy}
                    </Typography>
                  </div>

                  {/* Support Options */}
                  <div style={{ marginBottom: '1rem' }}>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold', mb: 0.5 }}>
                      Customer Support:
                    </Typography>
                    <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                      <Button variant="outlined" size="small" sx={{ fontSize: '0.7rem', py: 0.25 }}>
                        💬 Live Chat
                      </Button>
                      <Button variant="outlined" size="small" sx={{ fontSize: '0.7rem', py: 0.25 }}>
                        📞 Call Support
                      </Button>
                      <Button variant="outlined" size="small" sx={{ fontSize: '0.7rem', py: 0.25 }}>
                        📧 Email
                      </Button>
                    </div>
                  </div>

                  {/* Installation Guide */}
                  <div>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold', mb: 0.5 }}>
                      Installation & Guides:
                    </Typography>
                    <Button variant="text" size="small" fullWidth sx={{ fontSize: '0.7rem', justifyContent: 'flex-start' }}>
                      📄 Download PDF Guide
                    </Button>
                    <Button variant="text" size="small" fullWidth sx={{ fontSize: '0.7rem', justifyContent: 'flex-start' }}>
                      🎥 Watch Video Tutorial
                    </Button>
                  </div>
                </div>
              </Box>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailView;
