import React, { useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Button,
  Chip,
  Rating,
  Card,
  CardMedia,
  Divider,
  Stack,
  Paper,
  List,
  ListItem,
  ListItemText,
  Tab,
  Tabs
} from '@mui/material';
import './ProductDetailview.css';

const ProductDetailView = ({ product, onBack }) => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedTab, setSelectedTab] = useState(0);

  if (!product) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <Typography variant="h6">No product selected</Typography>
        <Button variant="contained" onClick={onBack} sx={{ mt: 2 }}>
          Back to Products
        </Button>
      </Container>
    );
  }

  const handleTabChange = (_, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4, overflowX: 'hidden' }}>
      {/* Back Button */}
      <Button
        variant="outlined"
        onClick={onBack}
        sx={{ mb: 3 }}
      >
        ← Back to Products
      </Button>

      <Grid container spacing={4}>
        {/* Left Side - Product Images */}
        <Grid item xs={12} md={6}>
          <Box sx={{ position: 'sticky', top: 20 }}>
            {/* Main Product Image */}
            <Card sx={{ mb: 2 }}>
              <CardMedia
                component="img"
                height="400"
                image={product.images[selectedImage]}
                alt={product.productName}
                sx={{ objectFit: 'cover' }}
              />
            </Card>

            {/* Image Thumbnails */}
            {product.images.length > 1 && (
              <Stack direction="row" spacing={1} sx={{ overflowX: 'auto' }}>
                {product.images.map((image, index) => (
                  <Card
                    key={index}
                    sx={{
                      minWidth: 80,
                      cursor: 'pointer',
                      border: selectedImage === index ? '2px solid primary.main' : 'none'
                    }}
                    onClick={() => setSelectedImage(index)}
                  >
                    <CardMedia
                      component="img"
                      height="80"
                      image={image}
                      alt={`${product.productName} view ${index + 1}`}
                      sx={{ objectFit: 'cover' }}
                    />
                  </Card>
                ))}
              </Stack>
            )}
          </Box>
        </Grid>

        {/* Right Side - Product Details */}
        <Grid item xs={12} md={6}>
          {/* Product Header */}
          <Box sx={{ mb: 3 }}>
            {/* Brand & SKU */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" color="primary" sx={{ fontWeight: 'bold' }}>
                {product.brand}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                SKU: {product.modelNumber}
              </Typography>
            </Box>

            {/* Product Name */}
            <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', mb: 2 }}>
              {product.productName}
            </Typography>

            {/* Short Description */}
            <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
              {product.shortDescription}
            </Typography>

            {/* Rating & Reviews */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Rating value={product.ratings.average} precision={0.1} readOnly />
              <Typography variant="body2" sx={{ ml: 1 }}>
                {product.ratings.average} ({product.ratings.totalReviews.toLocaleString()} reviews)
              </Typography>
              <Chip
                label={`${product.ratings.verifiedBuyers} verified buyers`}
                size="small"
                sx={{ ml: 2 }}
              />
            </Box>
          </Box>

          {/* Pricing Section */}
          <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="h3" component="span" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                ${product.pricing.offerPrice}
              </Typography>
              {product.pricing.retailPrice !== product.pricing.offerPrice && (
                <>
                  <Typography
                    variant="h5"
                    component="span"
                    sx={{
                      textDecoration: 'line-through',
                      color: 'text.secondary',
                      ml: 2
                    }}
                  >
                    ${product.pricing.retailPrice}
                  </Typography>
                  <Chip
                    label={`Save ${product.pricing.savings.percentage}%`}
                    color="error"
                    sx={{ ml: 2 }}
                  />
                </>
              )}
            </Box>

            {/* Time-based Offers */}
            {product.pricing.timeBasedOffer && (
              <Typography variant="body2" color="error.main" sx={{ mb: 1 }}>
                ⏰ {product.pricing.timeBasedOffer}
              </Typography>
            )}

            {/* EMI Options */}
            {product.pricing.emiOptions && (
              <Typography variant="body2" color="primary.main" sx={{ mb: 2 }}>
                💳 EMI: {product.pricing.emiOptions}
              </Typography>
            )}

            {/* Stock Status */}
            <Box sx={{ mb: 2 }}>
              <Chip
                label={product.availability.stockStatus}
                color={product.availability.stockStatus === 'In Stock' ? 'success' : 'warning'}
                sx={{ mr: 2 }}
              />
              <Typography variant="body2" component="span" color="text.secondary">
                📦 {product.availability.estimatedDelivery}
              </Typography>
            </Box>

            {/* Quantity Selector */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Typography variant="body1" sx={{ mr: 2 }}>Quantity:</Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
              >
                -
              </Button>
              <Typography sx={{ mx: 2, minWidth: 30, textAlign: 'center' }}>
                {quantity}
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setQuantity(quantity + 1)}
              >
                +
              </Button>
            </Box>

            {/* Action Buttons */}
            <Stack spacing={2}>
              <Button
                variant="contained"
                size="large"
                fullWidth
                sx={{ py: 1.5 }}
              >
                🛒 Add to Cart
              </Button>
              <Button
                variant="outlined"
                size="large"
                fullWidth
              >
                💰 Buy Now
              </Button>
              <Button
                variant="text"
                size="large"
                fullWidth
              >
                ❤️ Add to Wishlist
              </Button>
            </Stack>
          </Paper>

          {/* Vehicle Compatibility */}
          <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              🚗 Vehicle Compatibility
            </Typography>
            <Typography variant="body1" sx={{ mb: 2 }}>
              {product.fitment.vehicleCompatibility}
            </Typography>
            {product.fitment.fitmentGuarantee && (
              <Chip label="✅ Fitment Guarantee" color="success" />
            )}
          </Paper>

          {/* Shipping & Delivery */}
          <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              🚚 Shipping & Delivery
            </Typography>
            <Stack spacing={1}>
              <Typography variant="body2">
                {product.availability.freeShipping ? '✅ Free Shipping' : `Shipping: $${product.availability.shippingCharges}`}
              </Typography>
              <Typography variant="body2">
                📦 Estimated Delivery: {product.availability.estimatedDelivery}
              </Typography>
              <Typography variant="body2">
                {product.availability.codAvailable ? '💰 Cash on Delivery Available' : '💳 Online Payment Only'}
              </Typography>
              <Typography variant="body2">
                🔄 {product.availability.returnPolicy}
              </Typography>
            </Stack>
          </Paper>

          {/* Technical Specifications */}
          <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              🔧 Technical Specifications
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Weight:</Typography>
                <Typography variant="body1">{product.technicalDetails.weight}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Dimensions:</Typography>
                <Typography variant="body1">{product.technicalDetails.dimensions}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Material:</Typography>
                <Typography variant="body1">{product.technicalDetails.material}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Installation:</Typography>
                <Typography variant="body1">{product.technicalDetails.installationType}</Typography>
              </Grid>
            </Grid>

            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>Special Features:</Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {product.technicalDetails.specialFeatures.map((feature, index) => (
                  <Chip key={index} label={feature} size="small" />
                ))}
              </Stack>
            </Box>
          </Paper>

          {/* Warranty */}
          <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              🛡️ Warranty & Support
            </Typography>
            <Typography variant="body1" sx={{ mb: 2 }}>
              {product.warranty}
            </Typography>
            <Stack direction="row" spacing={2}>
              {product.support.chatSupport && (
                <Button variant="outlined" size="small">💬 Chat Support</Button>
              )}
              {product.support.callSupport && (
                <Button variant="outlined" size="small">📞 Call Support</Button>
              )}
            </Stack>
          </Paper>
        </Grid>
      </Grid>

      {/* Product Details Tabs */}
      <Box sx={{ mt: 4 }}>
        <Tabs value={selectedTab} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Description" />
          <Tab label="Specifications" />
          <Tab label="Reviews" />
          <Tab label="Q&A" />
          <Tab label="Installation" />
        </Tabs>

        {/* Tab Content */}
        <Box sx={{ mt: 3 }}>
          {selectedTab === 0 && (
            <Paper elevation={1} sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>Product Description</Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {product.shortDescription}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Category: {product.category}
              </Typography>
            </Paper>
          )}

          {selectedTab === 1 && (
            <Paper elevation={1} sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>Technical Specifications</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Physical Specifications</Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary="Weight" secondary={product.technicalDetails.weight} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Dimensions" secondary={product.technicalDetails.dimensions} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Material" secondary={product.technicalDetails.material} />
                    </ListItem>
                  </List>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Installation & Usage</Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary="Installation Type" secondary={product.technicalDetails.installationType} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Usage" secondary={product.technicalDetails.usage} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Finish" secondary={product.technicalDetails.finish} />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </Paper>
          )}

          {selectedTab === 2 && (
            <Paper elevation={1} sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>Customer Reviews</Typography>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Rating value={product.ratings.average} precision={0.1} readOnly />
                  <Typography variant="h6" sx={{ ml: 2 }}>
                    {product.ratings.average} out of 5
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Based on {product.ratings.totalReviews.toLocaleString()} reviews
                </Typography>
              </Box>

              {product.userTrust.featuredReviews.map((review, index) => (
                <Box key={index} sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2">{review.user}</Typography>
                    {review.verified && (
                      <Chip label="Verified Buyer" size="small" color="success" sx={{ ml: 1 }} />
                    )}
                  </Box>
                  <Rating value={review.rating} size="small" readOnly sx={{ mb: 1 }} />
                  <Typography variant="body2">{review.comment}</Typography>
                </Box>
              ))}
            </Paper>
          )}

          {selectedTab === 3 && (
            <Paper elevation={1} sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>Questions & Answers</Typography>
              {product.userTrust.qnaSection.map((qa, index) => (
                <Box key={index} sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Q: {qa.question}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    A: {qa.answer}
                  </Typography>
                  <Divider sx={{ mt: 2 }} />
                </Box>
              ))}
            </Paper>
          )}

          {selectedTab === 4 && (
            <Paper elevation={1} sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>Installation Guide</Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Installation Type: {product.technicalDetails.installationType}
              </Typography>
              <Stack spacing={2}>
                <Button variant="outlined" href={product.support.installationGuide} target="_blank">
                  📄 Download Installation Guide (PDF)
                </Button>
                <Button variant="outlined">
                  🎥 Watch Installation Video
                </Button>
                <Typography variant="body2" color="text.secondary">
                  Need help? Contact our support team for professional installation assistance.
                </Typography>
              </Stack>
            </Paper>
          )}
        </Box>
      </Box>
    </Container>
  );
};

export default ProductDetailView;
