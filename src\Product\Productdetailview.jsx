import React, { useState } from 'react';
import './ProductDetailview.css';
import Box from '../components/Box/Box.jsx';
import {
  Typography,
  Button,
  Chip,
  Rating,
  LinearProgress,
  Avatar
} from '@mui/material';

const ProductDetailView = ({ product, onBack }) => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);

  if (!product) {
    return (
      <div className="landing-page">
        <Box className="nav-bar">
          <Typography variant="h6" sx={{ color: 'white', p: 2 }}>No Product Selected</Typography>
        </Box>
        <div style={{ padding: '2rem', textAlign: 'center' }}>
          <Button variant="contained" onClick={onBack}>
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="landing-page">
      {/* Top Navigation Bar */}
      <Box className="nav-bar">
        <div className="nav-bar-content">
          <Button
            variant="text"
            onClick={onBack}
            sx={{ color: 'white', mr: 2 }}
          >
            ← Back to Products
          </Button>
          <Typography variant="h6" className="nav-bar-title">
            {product.brand} - {product.productName}
          </Typography>
          <Typography variant="body2" className="nav-bar-sku">
            SKU: {product.modelNumber}
          </Typography>
        </div>
      </Box>

      {/* Main Content Area */}
      <div className="main-content">
        {/* Left Sidebar - Product Image Gallery & 360° Views */}
        <Box className="left-sidebar">
          <div className="image-gallery-container">
            <Typography variant="caption" className="gallery-title">
              360° Views
            </Typography>

            {/* Main Selected Thumbnail - Larger */}
            <div className="main-image-container">
              <img
                src={product.images[selectedImage]}
                alt={`${product.productName} main view`}
                className="main-image"
              />
            </div>

            {/* All Image Thumbnails - Compact Grid */}
            <div className="thumbnail-grid">
              {product.images.map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={`View ${index + 1}`}
                  onClick={() => setSelectedImage(index)}
                  className={`thumbnail-image ${selectedImage === index ? 'selected' : ''}`}
                />
              ))}
            </div>

            {/* 360° View Button - Compact */}
            <Button
              variant="outlined"
              size="small"
              fullWidth
              className="view-360-button"
            >
              🔄 360°
            </Button>
          </div>
        </Box>

        {/* Center Content */}
        <div className="center-content">
          {/* Top Section */}
          <div className="top-section">
            {/* Hero Section - Main Product Image & Purchase Options */}
            <Box className="hero-section">
              <div className="hero-container">
                {/* Large Product Image - Maximized */}
                <div className="hero-image-container">
                  <img
                    src={product.images[selectedImage]}
                    alt={product.productName}
                    className="hero-image"
                  />
                  {/* Image overlay with quick info */}
                  <div className="image-overlay">
                    <Typography variant="caption" className="overlay-text">
                      {product.brand} • {product.modelNumber}
                    </Typography>
                  </div>
                </div>

                {/* Compact Product Info & Purchase - Horizontal Layout */}
                <div className="hero-info-grid">
                  {/* Product Info */}
                  <div className="product-info">
                    <Typography variant="subtitle2" className="product-title">
                      {product.productName.length > 30 ? product.productName.substring(0, 30) + '...' : product.productName}
                    </Typography>
                    <div className="product-rating-container">
                      <Rating value={product.ratings.average} precision={0.1} size="small" readOnly />
                      <Typography variant="caption" className="rating-text">
                        {product.ratings.average}
                      </Typography>
                    </div>
                    <Typography variant="h6" className="product-price">
                      ${product.pricing.offerPrice}
                    </Typography>
                  </div>

                  {/* Quantity Selector - Compact */}
                  <div className="quantity-selector">
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="quantity-button"
                    >
                      -
                    </Button>
                    <Typography className="quantity-text">
                      {quantity}
                    </Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setQuantity(quantity + 1)}
                      className="quantity-button"
                    >
                      +
                    </Button>
                  </div>

                  {/* Action Buttons - Vertical Stack */}
                  <div className="action-buttons">
                    <Button
                      variant="contained"
                      size="small"
                      className="compact-button"
                    >
                      🛒 Cart
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      className="compact-button"
                    >
                      💰 Buy
                    </Button>
                  </div>
                </div>
              </div>
            </Box>

            {/* Right Grid - Product Analytics & Variants */}
            <div className="right-grid">
              {/* Sales Analytics */}
              <Box className="grid-item">
                <div className="grid-item-content">
                  <Typography variant="caption" className="grid-item-title">
                    📊 Sales
                  </Typography>
                  <Typography variant="h6" className="grid-item-value">
                    {product.salesData?.totalSold || '1.2K'}
                  </Typography>
                  <Typography variant="caption" className="grid-item-subtitle">
                    Sold
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={75}
                    className="grid-item-progress"
                  />
                </div>
              </Box>

              {/* Color Variants */}
              <Box className="grid-item">
                <div className="grid-item-content">
                  <Typography variant="caption" className="grid-item-title">
                    🎨 Colors
                  </Typography>
                  <div className="color-options">
                    {['#ff0000', '#0000ff', '#000000', '#c0c0c0'].map((color, index) => (
                      <div
                        key={index}
                        className="color-circle"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                  <Typography variant="caption" className="grid-item-subtitle">
                    4 Options
                  </Typography>
                </div>
              </Box>

              {/* Size Variants */}
              <Box className="grid-item">
                <div className="grid-item-content">
                  <Typography variant="caption" className="grid-item-title">
                    📏 Sizes
                  </Typography>
                  <div className="size-options">
                    {['S', 'M', 'L', 'XL'].map((size) => (
                      <div key={size} className="size-option">
                        {size}
                      </div>
                    ))}
                  </div>
                  <Typography variant="caption" className="grid-item-subtitle">
                    4 Sizes
                  </Typography>
                </div>
              </Box>

              {/* Stock Status */}
              <Box className="grid-item">
                <div className="grid-item-content">
                  <Typography variant="caption" className="grid-item-title">
                    📦 Stock
                  </Typography>
                  <Chip
                    label={product.availability.stockStatus === 'In Stock' ? 'In Stock' : 'Low'}
                    color={product.availability.stockStatus === 'In Stock' ? 'success' : 'warning'}
                    size="small"
                    className="compact-chip"
                  />
                  <Typography variant="caption" className="grid-item-subtitle">
                    2-3 Days
                  </Typography>
                </div>
              </Box>

              {/* Customer Rating Distribution */}
              <Box className="grid-item">
                <div className="grid-item-content">
                  <Typography variant="caption" className="grid-item-title">
                    ⭐ Rating
                  </Typography>
                  <Typography variant="h6" className="grid-item-value" sx={{ color: 'warning.main' }}>
                    {product.ratings.average}
                  </Typography>
                  <Typography variant="caption" className="grid-item-subtitle">
                    {Math.floor(product.ratings.totalReviews/1000)}K Reviews
                  </Typography>
                  <div className="rating-bars">
                    <LinearProgress variant="determinate" value={90} className="rating-bar" />
                    <LinearProgress variant="determinate" value={70} className="rating-bar" />
                    <LinearProgress variant="determinate" value={50} className="rating-bar" />
                  </div>
                </div>
              </Box>

              {/* Warranty & Support */}
              <Box className="grid-item">
                <div className="grid-item-content">
                  <Typography variant="caption" className="grid-item-title">
                    🛡️ Warranty
                  </Typography>
                  <Typography variant="body2" className="grid-item-subtitle">
                    {product.warranty.split(' ').slice(0, 2).join(' ')}
                  </Typography>
                  <Chip
                    label="24/7"
                    size="small"
                    color="primary"
                    className="compact-chip"
                  />
                </div>
              </Box>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="bottom-section">
            {/* Left Column - Product Details & FAQs */}
            <div className="left-column">
              {/* Technical Specifications */}
              <Box className="left-item">
                <div className="left-item-content">
                  <Typography variant="subtitle2" className="section-title">
                    🔧 Technical Specs
                  </Typography>
                  <div className="specs-grid">
                    <div>
                      <Typography variant="caption" className="spec-label">Weight:</Typography>
                      <Typography variant="caption" className="spec-value">{product.technicalDetails.weight}</Typography>
                    </div>
                    <div>
                      <Typography variant="caption" className="spec-label">Size:</Typography>
                      <Typography variant="caption" className="spec-value">{product.technicalDetails.dimensions}</Typography>
                    </div>
                    <div>
                      <Typography variant="caption" className="spec-label">Material:</Typography>
                      <Typography variant="caption" className="spec-value">{product.technicalDetails.material}</Typography>
                    </div>
                    <div>
                      <Typography variant="caption" className="spec-label">Install:</Typography>
                      <Typography variant="caption" className="spec-value">{product.technicalDetails.installationType}</Typography>
                    </div>
                  </div>

                  {/* Special Features - Compact */}
                  <Typography variant="caption" className="spec-label" sx={{ mt: 0.5, display: 'block' }}>Features:</Typography>
                  <div className="features-container">
                    {product.technicalDetails.specialFeatures.slice(0, 4).map((feature, index) => (
                      <Chip
                        key={index}
                        label={feature}
                        size="small"
                        className="feature-chip"
                      />
                    ))}
                  </div>
                </div>
              </Box>

              {/* Customer Reviews & Ratings */}
              <Box className="left-item">
                <div className="left-item-content">
                  <Typography variant="subtitle2" className="section-title">
                    ⭐ Reviews ({product.ratings.totalReviews})
                  </Typography>
                  {product.userTrust.featuredReviews.slice(0, 3).map((review, index) => (
                    <div key={index} className="review-item">
                      <div className="review-header">
                        <Avatar className="review-avatar">
                          {review.user[0]}
                        </Avatar>
                        <Typography variant="caption" className="review-user">
                          {review.user.split(' ')[0]}
                        </Typography>
                        {review.verified && (
                          <Chip label="✓" size="small" color="success" className="review-verified" />
                        )}
                        <Rating value={review.rating} size="small" readOnly className="review-rating" />
                      </div>
                      <Typography variant="body2" className="review-comment">
                        "{review.comment.length > 80 ? review.comment.substring(0, 80) + '...' : review.comment}"
                      </Typography>
                    </div>
                  ))}

                  {/* Quick Stats */}
                  <div className="review-stats">
                    <Typography variant="caption" className="review-stats-text">
                      Avg: {product.ratings.average}★ | {product.ratings.verifiedBuyers} Verified
                    </Typography>
                  </div>
                </div>
              </Box>

              {/* FAQs */}
              <Box className="left-item">
                <div className="left-item-content">
                  <Typography variant="subtitle2" className="section-title">
                    ❓ FAQs
                  </Typography>
                  {product.userTrust.qnaSection.slice(0, 4).map((qa, index) => (
                    <div key={index} className="faq-item">
                      <Typography variant="body2" className="faq-question">
                        Q: {qa.question.length > 50 ? qa.question.substring(0, 50) + '...' : qa.question}
                      </Typography>
                      <Typography variant="body2" className="faq-answer">
                        A: {qa.answer.length > 80 ? qa.answer.substring(0, 80) + '...' : qa.answer}
                      </Typography>
                    </div>
                  ))}

                  {/* Quick Links */}
                  <div className="faq-actions">
                    <Button variant="text" size="small" className="faq-action-button">
                      📄 Manual
                    </Button>
                    <Button variant="text" size="small" className="faq-action-button">
                      🎥 Video
                    </Button>
                    <Button variant="text" size="small" className="faq-action-button">
                      💬 Ask
                    </Button>
                  </div>
                </div>
              </Box>
            </div>

            {/* Right Column - Purchase Analytics & Related Info */}
            <div className="right-column">
              {/* Sales Graph & Purchase Analytics */}
              <Box className="right-item">
                <div className="right-item-content">
                  <Typography variant="subtitle2" className="section-title">
                    📈 Sales Analytics & Purchase Trends
                  </Typography>

                  {/* Sales Numbers */}
                  <div className="sales-grid">
                    <div className="sales-stat primary">
                      <Typography variant="h6" className="sales-stat-value">
                        {product.salesData?.totalSold || '1,234'}
                      </Typography>
                      <Typography variant="caption" className="sales-stat-label">Total Sold</Typography>
                    </div>
                    <div className="sales-stat success">
                      <Typography variant="h6" className="sales-stat-value">
                        {product.salesData?.thisMonth || '156'}
                      </Typography>
                      <Typography variant="caption" className="sales-stat-label">This Month</Typography>
                    </div>
                  </div>

                  {/* Purchase Trends */}
                  <Typography variant="body2" className="purchase-activity-title">
                    Recent Purchase Activity:
                  </Typography>
                  <div>
                    {['John D. bought 2 hours ago', 'Sarah M. bought 5 hours ago', 'Mike R. bought 1 day ago'].map((activity, index) => (
                      <Typography key={index} variant="caption" className="activity-item">
                        🛒 {activity}
                      </Typography>
                    ))}
                  </div>

                  {/* Popularity Metrics */}
                  <div className="popularity-metrics">
                    <div className="popularity-item">
                      <Typography variant="caption" className="popularity-label">Popularity</Typography>
                      <LinearProgress variant="determinate" value={85} className="popularity-bar" />
                    </div>
                    <div className="popularity-item">
                      <Typography variant="caption" className="popularity-label">Demand</Typography>
                      <LinearProgress variant="determinate" value={92} className="popularity-bar" />
                    </div>
                  </div>

                  {/* Best Selling Variants */}
                  <Typography variant="body2" className="best-variants-title">
                    🏆 Best Selling Variants:
                  </Typography>
                  <div className="variant-chips">
                    <Chip label="Black - L" size="small" color="primary" />
                    <Chip label="Blue - M" size="small" color="primary" />
                    <Chip label="Red - XL" size="small" color="primary" />
                  </div>
                </div>
              </Box>

              {/* Shipping, Warranty & Support */}
              <Box className="right-item">
                <div className="right-item-content">
                  <Typography variant="subtitle2" className="section-title">
                    🚚 Shipping & Support Information
                  </Typography>

                  {/* Shipping Details */}
                  <div className="info-section shipping">
                    <Typography variant="body2" className="info-section-title">
                      Shipping Options:
                    </Typography>
                    <Typography variant="caption" className="info-item">
                      ✅ {product.availability.freeShipping ? 'Free Shipping' : 'Paid Shipping'}
                    </Typography>
                    <Typography variant="caption" className="info-item">
                      📦 Delivery: {product.availability.estimatedDelivery}
                    </Typography>
                    <Typography variant="caption" className="info-item">
                      💰 {product.availability.codAvailable ? 'COD Available' : 'Online Payment Only'}
                    </Typography>
                  </div>

                  {/* Warranty & Returns */}
                  <div className="info-section warranty">
                    <Typography variant="body2" className="info-section-title">
                      Warranty & Returns:
                    </Typography>
                    <Typography variant="caption" className="info-item">
                      🛡️ {product.warranty}
                    </Typography>
                    <Typography variant="caption" className="info-item">
                      🔄 {product.availability.returnPolicy}
                    </Typography>
                  </div>

                  {/* Support Options */}
                  <div>
                    <Typography variant="body2" className="info-section-title">
                      Customer Support:
                    </Typography>
                    <div className="support-buttons">
                      <Button variant="outlined" size="small" className="support-button">
                        💬 Live Chat
                      </Button>
                      <Button variant="outlined" size="small" className="support-button">
                        📞 Call Support
                      </Button>
                      <Button variant="outlined" size="small" className="support-button">
                        📧 Email
                      </Button>
                    </div>
                  </div>

                  {/* Installation Guide */}
                  <div>
                    <Typography variant="body2" className="info-section-title">
                      Installation & Guides:
                    </Typography>
                    <Button variant="text" size="small" fullWidth className="guide-button">
                      📄 Download PDF Guide
                    </Button>
                    <Button variant="text" size="small" fullWidth className="guide-button">
                      🎥 Watch Video Tutorial
                    </Button>
                  </div>
                </div>
              </Box>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailView;
