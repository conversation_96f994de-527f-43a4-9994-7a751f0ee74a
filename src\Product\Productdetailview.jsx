import React from 'react';
import './ProductDetailview.css';
import Box from '../components/Box/Box.jsx';

const ProductDetailView = () => {
  return (
    <div className="landing-page">
      {/* Top Navigation Bar */}
      <Box className="nav-bar">
        <span></span>
      </Box>

      {/* Main Content Area */}
      <div className="main-content">
        {/* Left Sidebar */}
        <Box className="left-sidebar">
          <span></span>
        </Box>

        {/* Center Content */}
        <div className="center-content">
          {/* Top Section */}
          <div className="top-section">
            {/* Hero Section */}
            <Box className="hero-section">
              <span></span>
            </Box>

            {/* Right Grid */}
            <div className="right-grid">
              <Box className="grid-item">
                <span></span>
              </Box>
              <Box className="grid-item">
                <span></span>
              </Box>
              <Box className="grid-item">
                <span></span>
              </Box>
              <Box className="grid-item">
                <span></span>
              </Box>
              <Box className="grid-item">
                <span></span>
              </Box>
              <Box className="grid-item">
                <span></span>
              </Box>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="bottom-section">
            {/* Left Column */}
            <div className="left-column">
              <Box className="left-item">
                <span></span>
              </Box>
              <Box className="left-item">
                <span></span>
              </Box>
              <Box className="left-item">
                <span></span>
              </Box>
            </div>

            {/* Right Column */}
            <div className="right-column">
              <Box className="right-item">
                <span></span>
              </Box>
              <Box className="right-item">
                <span></span>
              </Box>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};



